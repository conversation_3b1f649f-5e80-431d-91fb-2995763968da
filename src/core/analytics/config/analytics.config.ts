import { registerAs } from '@nestjs/config';
import { AnalyticsProvider } from '../analytics.interface';

export default registerAs('analytics', () => ({
  provider: process.env.ANALYTICS_PROVIDER || AnalyticsProvider.POSTHOG,
  enabled: process.env.ANALYTICS_ENABLED === 'true',

  // PostHog configuration
  posthog: {
    apiKey: process.env.POSTHOG_API_KEY || '',
    host: process.env.POSTHOG_HOST || 'https://app.posthog.com',
    enabled: process.env.POSTHOG_ENABLED === 'true',
  },

  // Segment configuration
  segment: {
    writeKey: process.env.SEGMENT_WRITE_KEY || '',
    enabled: process.env.SEGMENT_ENABLED === 'true',
  },

  // Google Analytics configuration
  googleAnalytics: {
    measurementId: process.env.GA_MEASUREMENT_ID || '',
    enabled: process.env.GA_ENABLED === 'true',
  },

  // Mixpanel configuration
  mixpanel: {
    token: process.env.MIXPANEL_TOKEN || '',
    enabled: process.env.MIXPANEL_ENABLED === 'true',
  },

  // Amplitude configuration
  amplitude: {
    apiKey: process.env.AMPLITUDE_API_KEY || '',
    enabled: process.env.AMPLITUDE_ENABLED === 'true',
  },
}));
