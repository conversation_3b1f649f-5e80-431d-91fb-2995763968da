import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AnalyticsProvider, IAnalyticsService } from '../analytics.interface';
import { PosthogService } from './posthog.service';
import { AllConfigType } from '@config/config.type';
import { NullAnalyticsService } from './null-analytics.service';

/**
 * Factory service that creates the appropriate analytics service based on configuration
 */
@Injectable()
export class AnalyticsFactoryService {
  private readonly logger = new Logger(AnalyticsFactoryService.name);

  constructor(private configService: ConfigService<AllConfigType>) {}

  /**
   * Create an analytics service based on the configured provider
   */
  createAnalyticsService(): IAnalyticsService {
    const provider = this.configService.get('analytics.provider', {
      infer: true,
    });
    const enabled = this.configService.get('analytics.enabled', {
      infer: true,
    });

    if (!enabled) {
      this.logger.log('Analytics is disabled');
      return new NullAnalyticsService();
    }

    this.logger.log(`Creating analytics service for provider: ${provider}`);

    switch (provider) {
      case AnalyticsProvider.POSTHOG:
        return new PosthogService(this.configService);

      // Add other providers here as they are implemented
      // case AnalyticsProvider.SEGMENT:
      //   return new SegmentService(this.configService);

      // case AnalyticsProvider.GOOGLE_ANALYTICS:
      //   return new GoogleAnalyticsService(this.configService);

      // case AnalyticsProvider.MIXPANEL:
      //   return new MixpanelService(this.configService);

      // case AnalyticsProvider.AMPLITUDE:
      //   return new AmplitudeService(this.configService);

      case AnalyticsProvider.NONE:
      default:
        this.logger.warn(
          `Unknown or unsupported analytics provider: ${provider}, using NullAnalyticsService`,
        );
        return new NullAnalyticsService();
    }
  }
}
