export const AUTH_CONSTANTS = {
  JWT_SECRET: process.env.AUTH_JWT_SECRET || 'no-one-will-check-this-lol-yash',
  SESSION_EXPIRATION: process.env.AUTH_SESSION_EXPIRES_IN || '20m', // 20 minutes
  SESSION_EXPIRATION_MS: process.env.AUTH_SESSION_EXPIRES_IN_MS || 1200000, // 20 minutes in ms
  ENCRYPTION_KEY:
    process.env.JWT_ENCRYPTION_KEY || 'a-strong-32-character-encryption-key!',
  COOKIE_OPTIONS: {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax' as const,
    path: '/',
  },
} as const;
