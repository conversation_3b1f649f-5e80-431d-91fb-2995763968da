import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { faker } from '@faker-js/faker';
import { AddressEntity } from '@app/business/address/addresses/infrastructure/entities/address.entity';
import { UserEntity } from '../../../../business/user/users/infrastructure/entities/user.entity';

@Injectable()
export class AddressFactory {
  constructor(
    @InjectRepository(AddressEntity)
    private repository: Repository<AddressEntity>,
    @InjectRepository(UserEntity)
    private userRepository: Repository<UserEntity>,
  ) {}

  async createRandomAddress() {
    const customer = await this.userRepository.find();
    return () => {
      const randomCustomer = faker.helpers.arrayElement(customer);
      return this.repository.create({
        tenantId: faker.string.uuid(),
        customerId: randomCustomer?.id,
        name: faker.company.name(),
        companyName: faker.company.name(),
        email: faker.internet.email(),
        countryCode: faker.location.countryCode(),
        phoneNumber: faker.number
          .int({ min: 1000000000, max: 9999999999 })
          .toString(),
        phoneExtension: faker.number.int({ min: 100, max: 999 }),
        addressLine1: faker.location.streetAddress(),
        addressLine2: faker.location.secondaryAddress(),
        city: faker.location.city(),
        province: faker.location.state(),
        postalCode: faker.location.zipCode(),
        country: faker.location.country(),
        notes: faker.lorem.sentence(),
        latitude: parseFloat(`${faker.location.latitude()}`),
        longitude: parseFloat(`${faker.location.longitude()}`),
        isFavoriteForPickup: faker.datatype.boolean(),
        isFavoriteForDelivery: faker.datatype.boolean(),
        isDefaultForPickup: faker.datatype.boolean(),
        isDefaultForDelivery: faker.datatype.boolean(),
      });
    };
  }
}
