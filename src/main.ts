import 'dotenv/config';
import {
  ClassSerializerInterceptor,
  ValidationPipe,
  VersioningType,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { NestFactory, Reflector } from '@nestjs/core';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { useContainer } from 'class-validator';
import cookieParser from 'cookie-parser';
import { networkInterfaces } from 'os';
import { AppModule } from './app.module';
import validationOptions from './utils/validation-options';
import { AllConfigType } from '@config/config.type';
import { ResolvePromisesInterceptor } from '@utils/serializer.interceptor';
import { TRACER_SERVICE } from '@core/infrastructure/tracing/tracer.interface';
import { TenantContextInterceptor } from '@core/auth/interceptors/tenant-context.interceptor';

// Helper function to get local IP addresses
const getLocalIpAddresses = (): string[] => {
  const interfaces = networkInterfaces();
  const addresses: string[] = [];

  Object.keys(interfaces).forEach((interfaceName) => {
    const networkInterface = interfaces[interfaceName];
    if (networkInterface) {
      networkInterface.forEach((iface) => {
        // Skip internal and non-IPv4 addresses
        if (!iface.internal && iface.family === 'IPv4') {
          addresses.push(iface.address);
        }
      });
    }
  });
  return addresses;
};

async function bootstrap() {
  const app = await NestFactory.create(AppModule, {
    cors: {
      origin: [
        'https://ui.hapito.app',
        'http://localhost:3001',
        'http://localhost:3002',
        'http://localhost:5173',
        'http://localhost:5174',
        'http://localhost:5175',
      ],
      credentials: true,
    },
  });
  app.use(cookieParser());

  useContainer(app.select(AppModule), { fallbackOnErrors: true });
  const configService = app.get(ConfigService<AllConfigType>);

  // Get tracer service using its token
  app.get(TRACER_SERVICE);

  app.enableShutdownHooks();
  app.setGlobalPrefix(
    configService.getOrThrow('app.apiPrefix', { infer: true }),
    {
      exclude: ['/'],
    },
  );
  app.enableVersioning({
    type: VersioningType.URI,
  });
  app.useGlobalPipes(new ValidationPipe(validationOptions));
  app.useGlobalInterceptors(
    new ResolvePromisesInterceptor(),
    new ClassSerializerInterceptor(app.get(Reflector)),
    new TenantContextInterceptor(),
  );

  app.getHttpAdapter().getInstance().disable('x-powered-by');
  // // Apply enterprise-grade security middleware
  // // Required for SOC2 compliance and OWASP Top 10 mitigation
  // // DO NOT MODIFY: Critical security component
  // app.use(new SecurityMiddleware().use.bind(new SecurityMiddleware()));

  const options = new DocumentBuilder()
    .setTitle('API')
    .setDescription('API docs')
    .setVersion('1.0')
    .addCookieAuth()
    .build();

  const document = SwaggerModule.createDocument(app, options);
  SwaggerModule.setup('docs', app, document);

  const port = configService.getOrThrow('app.port', { infer: true });

  // Listen on all network interfaces
  await app.listen(port, '0.0.0.0');

  // Print server URLs
  const localIps = getLocalIpAddresses();
  console.log('\n🚀 Server running on:');
  localIps.forEach((ip) => {
    console.log(`http://${ip}:${port}`);
  });
}

void bootstrap();
