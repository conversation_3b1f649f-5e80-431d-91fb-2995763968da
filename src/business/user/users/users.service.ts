import { forwardRef, Injectable, Inject, HttpStatus } from '@nestjs/common';
import { CreateUserDto } from './dto/create-user.dto';
import { QueryUserDto } from './dto/query-user.dto';
import { User } from './domain/user';
import bcrypt from 'bcryptjs';
import { UserOrigin, UserStatus, UserType } from './domain/user.types';
import { UsersRepository } from '@app/business/user/users/infrastructure/repositories/user.repository';
import { hash } from 'bcrypt';
import { CreateCustomerDto } from '@app/business/user/users/dto/create-customer.dto';
import { DataSource } from 'typeorm';
import {
  UserAlreadyExistsException,
  UserEmailExistsException,
  UserNotFoundException,
  UserTenantMismatchException,
  UserInvalidStatusException,
} from '@utils/errors/exceptions/user.exceptions';
import { AccountNumberService } from '@app/business/user/users/services/account-number.service';
import { AppException } from '@utils/errors/app.exception';
import { CustomerCategoriesService } from '@app/business/user/customer-categories/customer-categories.service';
import { UserMapper } from '@app/business/user/users/infrastructure/mappers/user.mapper';
import { UserEntity } from '@app/business/user/users/infrastructure/entities/user.entity';
import { UpdateCustomerDto } from '@app/business/user/users/dto/update-customer.dto';
import { ErrorCode } from '@utils/errors/error-codes';
import { PaginatedResult } from '@utils/query-creator/interfaces';
import { QueryService } from '@utils/query-creator/query.service';
import { CustomerResponseDto } from './dto/customer-response.dto';
import { CategoryResponseDto } from '@app/business/user/customer-categories/dto/category-response.dto';
import { CustomerDto } from '@app/business/user/users/dto/customer-dto.model';
import { PriceSetDto } from './dto/get-customer-price-sets.dto';
import { BaseFilterDto } from '../../../core/infrastructure/filtering/dtos/base-filter.dto';
import { SettingEntity } from '../../../core/settings/infrastructure/entities/settings.entity';
import { Scope } from '../../../core/settings/domain/settings.type';
import { ContactsService } from '@app/business/user/contacts/contacts.service';
import { DEFAULT_CONTACT_PERMISSIONS } from '@app/business/user/contacts/domain/contact-permissions';
import { ContactEntity } from '@app/business/user/contacts/infrastructure/persistence/relational/entities/contact.entity';

@Injectable()
export class UsersService {
  constructor(
    private readonly userRepository: UsersRepository,
    private readonly connection: DataSource,
    @Inject(forwardRef(() => CustomerCategoriesService))
    private readonly categoryService: CustomerCategoriesService,
    private readonly accountNumberService: AccountNumberService,
    private readonly queryService: QueryService,
    @Inject(forwardRef(() => ContactsService))
    private readonly contactsService: ContactsService,
  ) {}
  async createCustomerWithCategories(
    tenantId: string,
    createCustomerDto: CreateCustomerDto,
  ): Promise<CustomerResponseDto> {
    const queryRunner = this.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const existingUser = await this.findByEmail(createCustomerDto.email);

      if (existingUser) {
        throw new UserAlreadyExistsException(createCustomerDto.email, tenantId);
      }

      const { categories } = createCustomerDto;
      const passwordHash = await hash(Math.random().toString(36), 10);

      const userEntity = queryRunner.manager.create(UserEntity, {
        tenantId,
        accountNumber: createCustomerDto.accountNumber,
        userType: UserType.Customer,
        status: createCustomerDto.status || UserStatus.Active,
        origin: UserOrigin.MANUAL,
        emailVerified: true,
        loginCount: 0,
        failedAttempts: 0,
        companyName: createCustomerDto.companyName,
        contactName: createCustomerDto.contactName,
        email: createCustomerDto.email,
        phoneCountryCode: createCustomerDto.phoneCountryCode,
        phoneNumber: createCustomerDto.phoneNumber,
        phoneExtension: createCustomerDto.phoneExtension,
        faxCountryCode: createCustomerDto.faxCountryCode,
        faxNumber: createCustomerDto.faxNumber,
        website: createCustomerDto.website,
        addressLine1: createCustomerDto.addressLine1,
        addressLine2: createCustomerDto.addressLine2,
        city: createCustomerDto.city,
        province: createCustomerDto.province,
        postalCode: createCustomerDto.postalCode,
        country: createCustomerDto.country,
        password: passwordHash,
        isDeleted: false,
      });

      const savedUserEntity = await queryRunner.manager.save(userEntity);

      await queryRunner.manager.save(SettingEntity, [
        {
          tenantId,
          userId: userEntity.id,
          scope: Scope.User,
          key: 'customerGeneralSettings',
          value: {
            generalSetting: {
              prePaidOrders: true,
            },
          },
        },
        {
          tenantId,
          userId: userEntity.id,
          scope: Scope.User,
          key: 'customerNotificationSettings',
          value: {
            sms: {
              order: {
                orderEdit: {
                  admin: true,
                  sender: true,
                  partner: true,
                  recipient: true,
                },
                orderPickup: {
                  admin: true,
                  sender: true,
                  partner: true,
                  recipient: true,
                },
                orderPlaced: {
                  admin: true,
                  sender: true,
                  partner: true,
                  recipient: true,
                },
                orderCancelled: {
                  admin: true,
                  sender: true,
                  partner: true,
                  recipient: true,
                },
                orderCompleted: {
                  admin: true,
                  sender: true,
                  partner: true,
                  recipient: true,
                },
              },
            },
            email: {
              order: {
                orderEdit: {
                  admin: true,
                  sender: true,
                  partner: true,
                  recipient: true,
                },
                orderPickup: {
                  admin: true,
                  sender: true,
                  partner: true,
                  recipient: true,
                },
                orderPlaced: {
                  admin: true,
                  sender: true,
                  partner: true,
                  recipient: true,
                },
                orderCancelled: {
                  admin: true,
                  sender: true,
                  partner: true,
                  recipient: true,
                },
                orderCompleted: {
                  admin: true,
                  sender: true,
                  partner: true,
                  recipient: true,
                },
              },
            },
          },
        },
        {
          tenantId,
          userId: userEntity.id,
          scope: Scope.User,
          key: 'customerUIConfigurationSettings',
          value: {
            UIConfigurationSettings: [
              {
                label: 'Pickup',
                tooltip: 'Enter primary information about the delivery.',
                children: [
                  {
                    label: 'Company Name',
                    tooltip: 'Specify where the items should be delivered.',
                    children: [],
                    isVisible: true,
                    isRequired: true,
                    hasChildren: false,
                    placeholder: 'Enter the main delivery location',
                    fieldIdentifier: 'companyName',
                    isSystemRequired: false,
                  },
                  {
                    label: 'Address Line 1',
                    tooltip: 'Provide GPS coordinates for precise delivery.',
                    children: [],
                    isVisible: true,
                    isRequired: true,
                    hasChildren: false,
                    placeholder: 'Enter the main delivery location',
                    fieldIdentifier: 'addressLine1',
                    isSystemRequired: false,
                  },
                  {
                    label: 'Address Line 2',
                    tooltip: 'Specify the expected delivery time.',
                    children: [],
                    isVisible: true,
                    isRequired: true,
                    hasChildren: false,
                    placeholder: 'Enter the main delivery location',
                    fieldIdentifier: 'addressLine2',
                    isSystemRequired: false,
                  },
                  {
                    label: 'Comments',
                    tooltip: 'Specify the expected delivery time.',
                    children: [],
                    isVisible: true,
                    isRequired: true,
                    hasChildren: false,
                    placeholder: 'MM/DD/YYYY HH:MM',
                    fieldIdentifier: 'comments',
                    isSystemRequired: false,
                  },
                  {
                    label: 'City',
                    tooltip: 'Specify the expected delivery time.',
                    children: [],
                    isVisible: true,
                    isRequired: true,
                    hasChildren: false,
                    placeholder: 'MM/DD/YYYY HH:MM',
                    fieldIdentifier: 'city',
                    isSystemRequired: false,
                  },
                  {
                    label: 'Postal Code',
                    tooltip: 'Specify the expected delivery time.',
                    children: [],
                    isVisible: true,
                    isRequired: true,
                    hasChildren: false,
                    placeholder: 'MM/DD/YYYY HH:MM',
                    fieldIdentifier: 'postalCode',
                    isSystemRequired: false,
                  },
                  {
                    label: 'Phone',
                    tooltip: 'Specify the expected delivery time.',
                    children: [],
                    isVisible: true,
                    isRequired: true,
                    hasChildren: false,
                    placeholder: 'MM/DD/YYYY HH:MM',
                    fieldIdentifier: 'phone',
                    isSystemRequired: false,
                  },
                  {
                    label: 'Alternate Phone',
                    tooltip: 'Specify the expected delivery time.',
                    children: [],
                    isVisible: true,
                    isRequired: true,
                    hasChildren: false,
                    placeholder: 'MM/DD/YYYY HH:MM',
                    fieldIdentifier: 'alternatePhone',
                    isSystemRequired: false,
                  },
                  {
                    label: 'Email',
                    tooltip: 'Specify the expected delivery time.',
                    children: [],
                    isVisible: true,
                    isRequired: true,
                    hasChildren: false,
                    placeholder: 'MM/DD/YYYY HH:MM',
                    fieldIdentifier: 'email',
                    isSystemRequired: false,
                  },
                  {
                    label: 'Notes',
                    tooltip: 'Specify the expected delivery time.',
                    children: [],
                    isVisible: true,
                    isRequired: true,
                    hasChildren: false,
                    placeholder: 'MM/DD/YYYY HH:MM',
                    fieldIdentifier: 'notes',
                    isSystemRequired: false,
                  },
                ],
                isVisible: true,
                isRequired: true,
                hasChildren: true,
                fieldIdentifier: 'pickupLocation',
                isSystemRequired: false,
              },
              {
                label: 'Delivery',
                tooltip: 'Enter primary information about the delivery.',
                children: [
                  {
                    label: 'Company Name',
                    tooltip: 'Specify where the items should be delivered.',
                    children: [],
                    isVisible: true,
                    isRequired: true,
                    hasChildren: false,
                    placeholder: 'Enter the main delivery location',
                    fieldIdentifier: 'companyName',
                    isSystemRequired: false,
                  },
                  {
                    label: 'Address Line 1',
                    tooltip: 'Provide GPS coordinates for precise delivery.',
                    children: [],
                    isVisible: true,
                    isRequired: true,
                    hasChildren: false,
                    placeholder: 'Enter the main delivery location',
                    fieldIdentifier: 'addressLine1',
                    isSystemRequired: false,
                  },
                  {
                    label: 'Address Line 2',
                    tooltip: 'Specify the expected delivery time.',
                    children: [],
                    isVisible: true,
                    isRequired: true,
                    hasChildren: false,
                    placeholder: 'Enter the main delivery location',
                    fieldIdentifier: 'addressLine2',
                    isSystemRequired: false,
                  },
                  {
                    label: 'Comments',
                    tooltip: 'Specify the expected delivery time.',
                    children: [],
                    isVisible: true,
                    isRequired: true,
                    hasChildren: false,
                    placeholder: 'MM/DD/YYYY HH:MM',
                    fieldIdentifier: 'comments',
                    isSystemRequired: false,
                  },
                  {
                    label: 'City',
                    tooltip: 'Specify the expected delivery time.',
                    children: [],
                    isVisible: true,
                    isRequired: true,
                    hasChildren: false,
                    placeholder: 'MM/DD/YYYY HH:MM',
                    fieldIdentifier: 'city',
                    isSystemRequired: false,
                  },
                  {
                    label: 'Postal Code',
                    tooltip: 'Specify the expected delivery time.',
                    children: [],
                    isVisible: true,
                    isRequired: true,
                    hasChildren: false,
                    placeholder: 'MM/DD/YYYY HH:MM',
                    fieldIdentifier: 'postalCode',
                    isSystemRequired: false,
                  },
                  {
                    label: 'Phone',
                    tooltip: 'Specify the expected delivery time.',
                    children: [],
                    isVisible: true,
                    isRequired: true,
                    hasChildren: false,
                    placeholder: 'MM/DD/YYYY HH:MM',
                    fieldIdentifier: 'phone',
                    isSystemRequired: false,
                  },
                  {
                    label: 'Alternate Phone',
                    tooltip: 'Specify the expected delivery time.',
                    children: [],
                    isVisible: true,
                    isRequired: true,
                    hasChildren: false,
                    placeholder: 'MM/DD/YYYY HH:MM',
                    fieldIdentifier: 'alternatePhone',
                    isSystemRequired: false,
                  },
                  {
                    label: 'Email',
                    tooltip: 'Specify the expected delivery time.',
                    children: [],
                    isVisible: true,
                    isRequired: true,
                    hasChildren: false,
                    placeholder: 'MM/DD/YYYY HH:MM',
                    fieldIdentifier: 'email',
                    isSystemRequired: false,
                  },
                  {
                    label: 'Notes',
                    tooltip: 'Specify the expected delivery time.',
                    children: [],
                    isVisible: true,
                    isRequired: true,
                    hasChildren: false,
                    placeholder: 'MM/DD/YYYY HH:MM',
                    fieldIdentifier: 'notes',
                    isSystemRequired: false,
                  },
                ],
                isVisible: true,
                isRequired: true,
                hasChildren: true,
                placeholder: 'MM/DD/YYYY HH:MM',
                fieldIdentifier: 'deliveryLocation',
                isSystemRequired: false,
              },
            ],
          },
        },
      ]);

      await queryRunner.commitTransaction();

      // Response object to return
      const response = new CustomerResponseDto();
      response.id = savedUserEntity.id;
      response.companyName = savedUserEntity.companyName;
      response.contactName = savedUserEntity.contactName;
      response.email = savedUserEntity.email;
      response.accountNumber = savedUserEntity.accountNumber;
      response.phoneCountryCode = savedUserEntity.phoneCountryCode;
      response.phoneNumber = savedUserEntity.phoneNumber;
      response.phoneExtension = savedUserEntity.phoneExtension;
      response.faxCountryCode = savedUserEntity.faxCountryCode;
      response.faxNumber = savedUserEntity.faxNumber;
      response.website = savedUserEntity.website;
      response.addressLine1 = savedUserEntity.addressLine1;
      response.addressLine2 = savedUserEntity.addressLine2;
      response.city = savedUserEntity.city;
      response.province = savedUserEntity.province;
      response.postalCode = savedUserEntity.postalCode;
      response.country = savedUserEntity.country;
      response.status = savedUserEntity.status;
      response.createdAt = savedUserEntity.createdAt;
      response.updatedAt = savedUserEntity.updatedAt;
      response.categories = [];

      // Process categories if present
      if (categories && categories.length > 0) {
        const categoryIds = await this.categoryService.processCategories(
          tenantId,
          categories,
        );

        if (categoryIds.length > 0) {
          await this.categoryService.assignToUser(
            savedUserEntity.id,
            categoryIds,
          );

          // Fetch the assigned categories to include in response
          const assignedCategories =
            await this.categoryService.findByIds(categoryIds);
          response.categories = assignedCategories.map((category) => {
            const categoryDto = new CategoryResponseDto();
            categoryDto.id = category.id;
            categoryDto.name = category.name;
            return categoryDto;
          });
        }
      }

      // Create a primary contact with the same email address
      try {
        // Create contact with enhanced permissions
        const enhancedPermissions = {
          ...DEFAULT_CONTACT_PERMISSIONS,
          address: true, // Give access to address management
        };

        // Create the contact
        const contactEntity = await this.contactsService.create(
          tenantId,
          savedUserEntity.id,
          {
            name: createCustomerDto.contactName,
            email: createCustomerDto.email,
            phoneCountryCode: createCustomerDto.phoneCountryCode,
            phoneNumber: createCustomerDto.phoneNumber,
            phoneExtension: createCustomerDto.phoneExtension,
            isActive: true,
            permissions: enhancedPermissions,
            // Only pass categories without filtering by type since CategorySelectionDto doesn't have a type field
            categories: categories || [],
          },
        );

        // Set the contact as primary using a direct database update
        if (contactEntity && contactEntity.id) {
          await this.connection
            .getRepository(ContactEntity)
            .update(contactEntity.id, { isPrimary: true });
        }
      } catch (contactError) {
        // Log the error but don't fail the customer creation
        console.error('Failed to create primary contact:', contactError);
      }

      return response;
    } catch (error) {
      await queryRunner.rollbackTransaction();

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        `Failed to create customer: ${error.message}`,
        ErrorCode.USER_ALREADY_EXISTS,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    } finally {
      await queryRunner.release();
    }
  }

  async updateCustomerWithCategories(
    tenantId: string,
    userId: string,
    updateCustomerDto: UpdateCustomerDto,
  ): Promise<User> {
    const existingUser = await this.userRepository.findById(userId);

    if (!existingUser) {
      throw new UserNotFoundException(userId);
    }

    if (existingUser.tenantId !== tenantId) {
      throw new UserTenantMismatchException(userId, tenantId);
    }

    if (
      updateCustomerDto.email &&
      updateCustomerDto.email !== existingUser.email
    ) {
      const userWithSameEmail = await this.userRepository.findByEmail(
        updateCustomerDto.email,
      );
      if (userWithSameEmail && userWithSameEmail.id !== userId) {
        throw new UserEmailExistsException(updateCustomerDto.email);
      }
    }

    try {
      const { categories, ...customerDataWithoutCategories } =
        updateCustomerDto;

      const updatedUserData = {
        ...existingUser,
        ...customerDataWithoutCategories,
        tenantId,
        userType: existingUser.userType,
      };
      const savedUser = await this.connection
        .getRepository(UserEntity)
        .save(updatedUserData);

      const customer = UserMapper.toDomain(savedUser);

      if (categories && categories.length > 0) {
        try {
          const categoryIds = await this.categoryService.processCategories(
            tenantId,
            categories,
          );

          if (categoryIds.length > 0) {
            await this.categoryService.assignToUser(userId, categoryIds);
          }
        } catch (categoryError) {
          console.error('Failed to update categories:', categoryError);
        }
      }

      return customer;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        `Failed to update customer: ${error.message}`,
        ErrorCode.USER_OPERATION_NOT_ALLOWED,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async create(createUserDto: CreateUserDto): Promise<User> {
    try {
      const hashedPassword = await hash(Math.random().toString(), 10);
      const user = await this.userRepository.create({
        ...createUserDto,
        password: hashedPassword,
        emailVerified: true,
        status: UserStatus.Active,
        origin: UserOrigin.Local,
        loginCount: 0,
        failedAttempts: 0,
        userType: UserType.Customer,
      });

      return user;
    } catch (error) {
      throw error;
    }
  }

  async findAll(
    tenantId: string,
    query: QueryUserDto,
  ): Promise<[User[], number]> {
    try {
      const [users, total] = await this.userRepository.findAll(tenantId, query);
      return [users, total];
    } catch (error) {
      throw error;
    }
  }

  async findById(id: string): Promise<User | null> {
    const user = await this.userRepository.findById(id);
    return user;
  }

  async findByEmail(email: string): Promise<User | null> {
    const user = await this.userRepository.findByEmail(email);
    return user;
  }

  async update(
    tenantId: string,
    id: string,
    updateData: Partial<User>,
  ): Promise<User> {
    try {
      if (updateData.password) {
        const salt = await bcrypt.genSalt();
        updateData.password = await bcrypt.hash(updateData.password, salt);
      }

      const user = await this.userRepository.update(tenantId, id, updateData);
      return user;
    } catch (error) {
      throw error;
    }
  }

  async softDelete(tenantId: string, id: string): Promise<void> {
    try {
      const existingUser = await this.userRepository.findById(id);

      if (!existingUser) {
        throw new UserNotFoundException(id);
      }

      existingUser.status = UserStatus.Active;
      existingUser.isDeleted = true;
      existingUser.deletedAt = new Date();
      await this.updateCustomerWithCategories(tenantId, id, existingUser);
    } catch (error) {
      throw error;
    }
  }

  async hardDelete(tenantId: string, id: string): Promise<void> {
    try {
      await this.userRepository.hardDelete(tenantId, id);
    } catch (error) {
      throw error;
    }
  }

  async restore(tenantId: string, id: string): Promise<void> {
    try {
      await this.userRepository.restore(tenantId, id);
    } catch (error) {
      throw error;
    }
  }

  async findTenantCustomers(
    tenantId: string,
    filter: BaseFilterDto,
  ): Promise<PaginatedResult<CustomerDto>> {
    const customer = await this.userRepository.find(tenantId, filter);
    return customer;
  }

  async getTenantCustomer(tenantId: string, customerId: string): Promise<User> {
    const user = await this.userRepository.findById(customerId);

    if (!user) {
      throw new UserNotFoundException(customerId);
    }

    if (user.tenantId !== tenantId) {
      throw new UserTenantMismatchException(customerId, tenantId);
    }

    if (user.userType !== UserType.Customer) {
      throw new UserInvalidStatusException(
        customerId,
        user.userType,
        UserType.Customer,
      );
    }

    return user;
  }

  async getAllCustomers(tenantId: string): Promise<UserEntity[]> {
    const customer = this.userRepository.findAllMinimal(tenantId);
    return customer;
  }

  async assignPriceSets(
    customerId: User['id'],
    priceSetIds: string[],
  ): Promise<void> {
    const customer = await this.userRepository.findById(customerId);
    if (!customer) {
      throw new UserNotFoundException(customerId);
    }

    await this.userRepository.assignPriceSets(customerId, priceSetIds);
    return;
  }

  async getCustomerPriceSets(customerId: User['id']): Promise<PriceSetDto[]> {
    const customer = await this.userRepository.findById(customerId);
    if (!customer) {
      throw new UserNotFoundException(customerId);
    }

    const priceSets =
      await this.userRepository.findPriceSetsByCustomerId(customerId);
    return priceSets;
  }
}
