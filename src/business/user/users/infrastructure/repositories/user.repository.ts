/* eslint-disable @typescript-eslint/no-unused-vars */
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { DeepPartial, In, Repository } from 'typeorm';
import { UserEntity } from '../entities/user.entity';
import { NullableType } from '@utils/types/nullable.type';
import { User } from '../../domain/user';
import { UserMapper } from '../mappers/user.mapper';
import { QueryUserDto } from '../../dto/query-user.dto';
import { PriceSetEntity } from '../../../../pricing/price-sets/infrastructure/entities/price-set.entity';
import { PriceSetCustomerEntity } from '../../../../pricing/price-sets/infrastructure/entities/price-set-customer.entity';
import { PriceSetNotFoundException } from '../../../../../utils/errors/exceptions/price-set-exceptions';
import { PriceSetDto } from '../../dto/get-customer-price-sets.dto';
import { BaseFilterDto } from '../../../../../core/infrastructure/filtering/dtos/base-filter.dto';
import { PaginatedResult } from '../../../../../utils/query-creator/interfaces';
import { UserStatus, UserType } from '../../domain/user.types';
import { SecureFilterService } from '../../../../../core/infrastructure/filtering/services/secure-filter.service';
import { UserFilterConfig } from '../../user-filter.config';
import { CustomerDto } from '../../dto/customer-dto.model';
import { CustomerMapper } from '../mappers/customer-mapper';

@Injectable()
export class UsersRepository {
  constructor(
    @InjectRepository(UserEntity)
    private readonly usersRepository: Repository<UserEntity>,
    @InjectRepository(PriceSetEntity)
    private readonly priceSetRepository: Repository<PriceSetEntity>,
    @InjectRepository(PriceSetCustomerEntity)
    private readonly priceSetCustomerRepository: Repository<PriceSetCustomerEntity>,
    private readonly filterService: SecureFilterService,
  ) {
    this.filterService = new SecureFilterService(UserFilterConfig());
  }

  async create(data: DeepPartial<User>): Promise<User> {
    const entityData = this.mapDomainToEntity(data);

    const newEntity = await this.usersRepository.save(entityData);
    return UserMapper.toDomain(newEntity);
  }

  private mapDomainToEntity(data: DeepPartial<User>): DeepPartial<UserEntity> {
    return {
      ...(data.id && { id: data.id }),
      ...(data.tenantId && { tenantId: data.tenantId }),
      ...(data.companyName && { company_name: data.companyName }),
      ...(data.contactName && { contact_name: data.contactName }),
      ...(data.accountNumber && { accountNumber: data.accountNumber }),
      ...(data.phoneNumberCountryCode && {
        phoneCountryCode: data.phoneNumberCountryCode,
      }),
      ...(data.phoneNumber && { phoneNumber: data.phoneNumber }),
      ...(data.phoneExtension && { phoneExtension: data.phoneExtension }),
      ...(data.faxNumber && { faxNumber: data.faxNumber }),
      ...(data.faxNumberCountryCode && {
        faxCountryCode: data.faxNumberCountryCode,
      }),
      ...(data.categories && { categories: data.categories }),
      ...(data.website && { website: data.website }),
      ...(data.email && { email: data.email }),
      ...(data.password && { password: data.password }),
      ...(data.emailVerified !== undefined && {
        emailVerified: data.emailVerified,
      }),
      ...(data.status !== undefined && { status: data.status }),
      ...(data.lastLoginAt && { lastLoginAt: data.lastLoginAt }),
      ...(data.loginCount !== undefined && { loginCount: data.loginCount }),
      ...(data.failedAttempts !== undefined && {
        failedAttempts: data.failedAttempts,
      }),
      ...(data.lockedUntil && { lockedUntil: data.lockedUntil }),
      ...(data.origin && { origin: data.origin }),
      ...(data.userType && { userType: data.userType }),
      ...(data.notificationSettings && {
        notificationSettings: data.notificationSettings,
      }),
      ...(data.metadata && { metadata: data.metadata }),
      ...(data.preferences && { preferences: data.preferences }),
      ...(data.addressLine1 && { addressLine1: data.addressLine1 }),
      ...(data.addressLine2 && { addressLine2: data.addressLine2 }),
      ...(data.city && { city: data.city }),
      ...(data.province && { province: data.province }),
      ...(data.postalCode && { postalCode: data.postalCode }),
      ...(data.country && { country: data.country }),
      ...(data.isDeleted !== undefined && { isDeleted: data.isDeleted }),
      ...(data.deletedAt && { deletedAt: data.deletedAt }),
      ...(data.createdAt && { createdAt: data.createdAt }),
      ...(data.updatedAt && { updatedAt: data.updatedAt }),
    };
  }

  async findById(id: string): Promise<NullableType<User>> {
    const entity = await this.usersRepository.findOne({
      where: {
        id,
      },
      relations: ['categories'],
    });

    return entity ? UserMapper.toDomain(entity) : null;
  }

  async findByEmail(email: User['email']): Promise<NullableType<User>> {
    if (!email) return null;

    const entity = await this.usersRepository.findOne({
      where: { email },
    });

    return entity ? UserMapper.toDomain(entity) : null;
  }

  async update(
    tenantId: string,
    id: User['id'],
    payload: Partial<User>,
  ): Promise<User> {
    const entity = await this.usersRepository.findOne({});

    if (!entity) {
      throw new Error('User not found');
    }

    const updatedEntity = await this.usersRepository.save(
      this.usersRepository.create(
        UserMapper.toPersistence({
          ...UserMapper.toDomain(entity),
          ...payload,
        }),
      ),
    );

    return UserMapper.toDomain(updatedEntity);
  }

  async remove(id: User['id']): Promise<void> {
    await this.usersRepository.softDelete(id);
  }

  findAll(tenantId: string, query: QueryUserDto): Promise<[User[], number]> {
    return Promise.resolve([[], 0]);
  }

  findOne(params: {
    tenantId: string;
    id?: string;
    email?: string;
  }): Promise<null> {
    return Promise.resolve(null);
  }

  hardDelete(tenantId: string, id: string): Promise<void> {
    return Promise.resolve(undefined);
  }

  restore(tenantId: string, id: string): Promise<void> {
    return Promise.resolve(undefined);
  }

  softDelete(tenantId: string, id: string): Promise<void> {
    return Promise.resolve(undefined);
  }

  async find(
    tenantId: string,
    filter: BaseFilterDto,
  ): Promise<PaginatedResult<CustomerDto>> {
    const queryBuilder = this.usersRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.categories', 'categories')
      .where('user.isDeleted = false')
      .andWhere('user.tenantId = :tenantId', { tenantId })
      .andWhere('user.userType = :userType', { userType: UserType.Customer });

    await this.filterService.buildQuery(queryBuilder, filter);
    const result = await this.filterService.executeQuery(queryBuilder, filter);
    const mappedData = result.data.map((result) =>
      CustomerMapper.fromEntity(result),
    );
    return { ...result, data: mappedData };
  }

  async findAllMinimal(tenantId: string): Promise<UserEntity[]> {
    const customer = await this.usersRepository.find({
      where: {
        isDeleted: false,
        userType: UserType.Customer,
        tenantId,
      },
      select: ['id', 'companyName', 'contactName'],
      order: { createdAt: 'DESC' },
    });
    return customer;
  }

  async assignPriceSets(
    customerId: User['id'],
    priceSetIds: string[],
  ): Promise<void> {
    const priceSets = await this.priceSetRepository.find({
      where: { id: In(priceSetIds) },
    });

    const foundPriceSetIds = new Set(priceSets.map((c) => c.id));
    const missingPriceSetIds = priceSetIds.filter(
      (id) => !foundPriceSetIds.has(id),
    );

    if (missingPriceSetIds.length > 0) {
      throw new PriceSetNotFoundException(
        missingPriceSetIds.length === 1
          ? missingPriceSetIds[0]
          : missingPriceSetIds,
      );
    }

    await this.priceSetCustomerRepository.delete({ customerId });

    const priceSetCustomers = priceSetIds.map((priceSetId) =>
      this.priceSetCustomerRepository.create({ setId: priceSetId, customerId }),
    );

    await this.priceSetCustomerRepository.save(priceSetCustomers);
  }

  async findPriceSetsByCustomerId(customerId: string): Promise<PriceSetDto[]> {
    const priceSets = this.priceSetCustomerRepository
      .createQueryBuilder('psc')
      .select([
        'psc.id as id',
        'psc.setId as "priceSetId"',
        'ps.name as name',
        'ps.internalName as "internalName"',
      ])
      .innerJoin(PriceSetEntity, 'ps', 'ps.id = psc.setId')
      .where('psc.customerId = :customerId', { customerId })
      .getRawMany();

    return priceSets;
  }
}
