import { Injectable } from '@nestjs/common';
import { BaseFilterDto } from '@core/infrastructure/filtering/dtos/base-filter.dto';
import { PaginatedResult } from '../../../utils/query-creator/interfaces';
import { DriverDomain } from './domain/driver';
import { DriverRepository } from './infrastructure/repository/driver.repository';
import {
  DriverAlreadyExistsException,
  DriverNotFoundException,
} from '../../../utils/errors/exceptions/driver.exceptions';
import { UserType } from '../users/domain/user.types';
import { DriverStatus } from '../../mobile/auth/domain/driver';

@Injectable()
export class DriverService {
  constructor(private readonly driverRepository: DriverRepository) {}

  async create(driverDomain: DriverDomain): Promise<DriverDomain> {
    const existingDriver = await this.driverRepository.findOne({
      tenantId: driverDomain.tenantId,
      email: driverDomain.email,
      userType: UserType.Driver,
    });
    if (existingDriver) {
      throw new DriverAlreadyExistsException(driverDomain.email);
    }

    const driver = await this.driverRepository.create(driverDomain);
    return driver;
  }

  async getDriverList(
    filter: BaseFilterDto,
    tenantId: string,
  ): Promise<PaginatedResult<DriverDomain>> {
    const driverDomain = await this.driverRepository.find(filter, tenantId);
    return driverDomain;
  }

  async getAllDrivers(tenantId: string): Promise<DriverDomain[]> {
    const driverDomain = this.driverRepository.findAll(tenantId);
    return driverDomain;
  }

  async getDriverDetails(driverId: DriverDomain['id']): Promise<DriverDomain> {
    const driverDomain = await this.driverRepository.findOne({
      id: driverId,
    });
    if (!driverDomain) {
      throw new DriverNotFoundException(driverId);
    }
    return driverDomain;
  }

  async updateDriverDetails(driverDomain: DriverDomain): Promise<void> {
    const driver = await this.driverRepository.findOne({
      id: driverDomain.id,
    });
    if (!driver) {
      throw new DriverNotFoundException(driverDomain.id);
    }

    if (driver.email !== driverDomain.email) {
      const existingDriver = await this.driverRepository.findOne({
        tenantId: driverDomain.tenantId,
        email: driverDomain.email,
        userType: UserType.Driver,
      });
      if (existingDriver) {
        throw new DriverAlreadyExistsException(driverDomain.email);
      }
    }

    await this.driverRepository.update(driverDomain);
    return;
  }

  async updateDriverStatus(
    driverId: string,
    status: DriverStatus,
  ): Promise<void> {
    const driver = await this.driverRepository.findOne({ id: driverId });
    if (!driver) {
      throw new DriverNotFoundException(driverId);
    }

    driver.driverStatus = status;
    await this.driverRepository.update(driver);
  }

  async deleteDriver(driverId: DriverDomain['id']): Promise<void> {
    const driver = await this.driverRepository.findOne({ id: driverId });
    if (!driver) {
      throw new DriverNotFoundException(driverId);
    }

    driver.isDeleted = true;
    driver.deletedAt = new Date();
    await this.driverRepository.update(driver);
    return;
  }
}
