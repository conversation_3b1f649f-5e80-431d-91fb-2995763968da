import { Injectable } from '@nestjs/common';
import {
  OrderStatus as MobileOrderStatus,
  mapMobileToBackendStatus,
  mapBackendToMobileStatus,
} from '../domain/order';
import { UpdateOrderStatusDto } from '../dto/update-order-status.dto';
import { AppException } from '@utils/errors/app.exception';
import { ErrorCode } from '@utils/errors/error-codes';
import { HttpStatus } from '@nestjs/common';
import { BaseFilterDto } from '@core/infrastructure/filtering/dtos/base-filter.dto';
import { FilterOperator } from '@core/infrastructure/filtering/types/filter.types';
import { OrdersService } from '@app/business/order/orders/orders.service';
import { OrderAssignmentService } from '@app/business/order/orders/services/order-assignment.service';
import { OrderStatusService } from '@app/business/order/orders/services/order-status.service';
import { UsersService } from '@app/business/user/users/users.service';
import { VehiclesService } from '@app/business/vehicle/vehicles/vehicles.service';
import { FileStorageService } from '@app/core/file-storage/file-storage.service';
import { PriceSetsService } from '@app/business/pricing/price-sets/price-sets.service';
import { OrderStatus as BackendOrderStatus } from '@app/business/order/orders/domain/order.types';

@Injectable()
export class MobileOrdersService {
  constructor(
    private readonly ordersService: OrdersService,
    private readonly orderAssignmentService: OrderAssignmentService,
    private readonly orderStatusService: OrderStatusService,
    private readonly usersService: UsersService,
    private readonly vehiclesService: VehiclesService,
    private readonly fileStorageService: FileStorageService,
    private readonly priceSetsService: PriceSetsService,
  ) {}

  async getDriverOrders(
    driverId: string,
    status?: MobileOrderStatus | MobileOrderStatus[],
  ) {
    // Get the driver to determine their tenant ID
    const driver = await this.usersService.findById(driverId);

    if (!driver) {
      throw new AppException(
        `Driver with ID ${driverId} not found`,
        ErrorCode.USER_NOT_FOUND,
        HttpStatus.NOT_FOUND,
      );
    }

    // Since filtering options are limited, we'll use basic pagination
    // and do the rest of the filtering in memory

    // Create a basic filter with just pagination
    const filter = new BaseFilterDto();
    filter.pageNumber = 1;
    filter.pageSize = 1000; // Get a large batch to ensure we have all driver's orders

    // Fetch all orders for this tenant
    const result = await this.ordersService.findAll(driver.tenantId, filter);

    // Filter the results in memory by driver ID
    let filteredOrders = result.data.filter(
      (order) => order.assignedDriverId === driverId,
    );

    // Apply status filtering in memory if needed
    if (status) {
      // Convert mobile status to backend status
      const backendStatuses = Array.isArray(status)
        ? status.map((s) => mapMobileToBackendStatus(s))
        : [mapMobileToBackendStatus(status)];

      // Filter by status
      filteredOrders = filteredOrders.filter((order) =>
        backendStatuses.includes(order.status),
      );
    }

    return filteredOrders;

    return result.data;
  }

  async getOrderById(id: string, driverId: string) {
    // Get the driver to determine their tenant ID
    const driver = await this.usersService.findById(driverId);

    if (!driver) {
      throw new AppException(
        `Driver with ID ${driverId} not found`,
        ErrorCode.USER_NOT_FOUND,
        HttpStatus.NOT_FOUND,
      );
    }

    try {
      return await this.ordersService.findOne(driver.tenantId, id);
    } catch {
      throw new AppException(
        `Order with ID ${id} not found`,
        ErrorCode.ORDER_NOT_FOUND,
        HttpStatus.NOT_FOUND,
      );
    }
  }

  async getDriverOrderById(driverId: string, orderId: string) {
    // Get the driver to determine their tenant ID
    const driver = await this.usersService.findById(driverId);

    if (!driver) {
      throw new AppException(
        `Driver with ID ${driverId} not found`,
        ErrorCode.USER_NOT_FOUND,
        HttpStatus.NOT_FOUND,
      );
    }

    try {
      const order = await this.ordersService.findOne(driver.tenantId, orderId);

      if (order.assignedDriverId !== driverId) {
        throw new AppException(
          `Order with ID ${orderId} is not assigned to driver ${driverId}`,
          ErrorCode.ORDER_NOT_ASSIGNED_TO_DRIVER,
          HttpStatus.FORBIDDEN,
        );
      }

      return order;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        `Order with ID ${orderId} not found`,
        ErrorCode.ORDER_NOT_FOUND,
        HttpStatus.NOT_FOUND,
      );
    }
  }

  async updateOrderStatus(
    driverId: string,
    orderId: string,
    updateDto: UpdateOrderStatusDto,
  ) {
    // Get the driver to determine their tenant ID
    const driver = await this.usersService.findById(driverId);

    if (!driver) {
      throw new AppException(
        `Driver with ID ${driverId} not found`,
        ErrorCode.USER_NOT_FOUND,
        HttpStatus.NOT_FOUND,
      );
    }

    // Verify order exists and is assigned to driver
    const order = await this.getDriverOrderById(driverId, orderId);

    // Check if the status transition is valid
    const backendStatus = mapMobileToBackendStatus(updateDto.status);
    const currentBackendStatus = order.status;

    if (
      !this.orderStatusService.isValidTransition(
        currentBackendStatus,
        backendStatus,
      )
    ) {
      throw new AppException(
        `Invalid status transition from ${currentBackendStatus} to ${backendStatus}`,
        ErrorCode.ORDER_INVALID_STATUS_TRANSITION,
        HttpStatus.BAD_REQUEST,
      );
    }

    // Update the order status via the order status service
    await this.ordersService.changeStatus(
      driver.tenantId,
      orderId,
      driverId,
      backendStatus,
      updateDto.statusNotes || '',
      '',
    );

    // Process the uploaded files and metadata
    const metadata: Record<string, any> = {};
    let hasMetadataUpdates = false;

    // Handle proof of delivery image if provided
    if (updateDto.proofOfDeliveryImage) {
      const fileName = `pod-${orderId}-${Date.now()}.jpg`;
      const fileUrl = await this.fileStorageService.uploadBase64Image(
        updateDto.proofOfDeliveryImage,
        fileName,
        'proof-of-delivery',
      );

      metadata.proofOfDeliveryImages = [fileUrl];
      hasMetadataUpdates = true;
    }

    // Handle customer signature if provided
    if (updateDto.customerSignature) {
      metadata.customerSignature = updateDto.customerSignature;
      hasMetadataUpdates = true;
    }

    // Handle signature image if provided
    if (updateDto.signatureImage) {
      const fileName = `signature-${orderId}-${Date.now()}.jpg`;
      const fileUrl = await this.fileStorageService.uploadBase64Image(
        updateDto.signatureImage,
        fileName,
        'signatures',
      );

      metadata.signatureImage = fileUrl;
      hasMetadataUpdates = true;
    }

    // Update order metadata if there are any updates
    if (hasMetadataUpdates) {
      await this.ordersService.update(driver.tenantId, orderId, driverId, {
        metadata: metadata,
      });
    }

    // If the order is delivered or completed, record the actual delivery time
    if (
      backendStatus === BackendOrderStatus.Completed ||
      updateDto.status === MobileOrderStatus.Delivered
    ) {
      await this.ordersService.update(driver.tenantId, orderId, driverId, {
        actualDeliveryTime: new Date(),
      });
    }

    // If the order is in transit, record the actual collection time if not already set
    if (
      backendStatus === BackendOrderStatus.InProgress ||
      updateDto.status === MobileOrderStatus.InTransit
    ) {
      if (!order.actualCollectionTime) {
        await this.ordersService.update(driver.tenantId, orderId, driverId, {
          actualCollectionTime: new Date(),
        });
      }
    }

    // Return the updated order
    return this.ordersService.findOne(driver.tenantId, orderId);
  }

  async acceptOrder(driverId: string, orderId: string) {
    // Get the driver to determine their tenant ID
    const driver = await this.usersService.findById(driverId);

    if (!driver) {
      throw new AppException(
        `Driver with ID ${driverId} not found`,
        ErrorCode.USER_NOT_FOUND,
        HttpStatus.NOT_FOUND,
      );
    }

    try {
      const order = await this.ordersService.findOne(driver.tenantId, orderId);

      // Convert backend status to mobile status for comparison
      const mobileStatus = mapBackendToMobileStatus(order.status);
      if (mobileStatus !== MobileOrderStatus.Pending) {
        throw new AppException(
          `Order with ID ${orderId} is not in Pending status`,
          ErrorCode.ORDER_INVALID_STATUS_TRANSITION,
          HttpStatus.BAD_REQUEST,
        );
      }

      // Find the driver's active vehicle
      const filter = new BaseFilterDto();
      filter.pageNumber = 1;
      filter.pageSize = 10;
      filter.where = {
        driverId: { [FilterOperator.EQ]: driverId },
        isActive: { [FilterOperator.EQ]: true },
      };
      const vehiclesResult = await this.vehiclesService.getVehicleList(
        filter,
        driver.tenantId,
      );
      const vehicleId =
        vehiclesResult.data.length > 0 ? vehiclesResult.data[0].id : undefined;

      // Assign the order to the driver and update the status
      await this.orderAssignmentService.assignOrder(
        orderId,
        driverId,
        driverId, // Driver is assigning themselves
        vehicleId,
        'Driver accepted order via mobile app',
      );

      // Update the order status to Assigned
      await this.ordersService.changeStatus(
        driver.tenantId,
        orderId,
        driverId,
        BackendOrderStatus.Assigned,
        'Driver accepted order via mobile app',
        '',
      );

      // Return the updated order
      return this.ordersService.findOne(driver.tenantId, orderId);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        `Order with ID ${orderId} not found`,
        ErrorCode.ORDER_NOT_FOUND,
        HttpStatus.NOT_FOUND,
      );
    }
  }

  async rejectOrder(driverId: string, orderId: string, reason: string) {
    // Get the driver to determine their tenant ID
    const driver = await this.usersService.findById(driverId);

    if (!driver) {
      throw new AppException(
        `Driver with ID ${driverId} not found`,
        ErrorCode.USER_NOT_FOUND,
        HttpStatus.NOT_FOUND,
      );
    }

    const order = await this.getDriverOrderById(driverId, orderId);

    // Convert backend status to mobile status for comparison
    const mobileStatus = mapBackendToMobileStatus(order.status);
    if (
      ![MobileOrderStatus.Pending, MobileOrderStatus.Assigned].includes(
        mobileStatus,
      )
    ) {
      throw new AppException(
        `Cannot reject an order that is already ${order.status}`,
        ErrorCode.ORDER_INVALID_STATUS_TRANSITION,
        HttpStatus.BAD_REQUEST,
      );
    }

    // Unassign the order
    await this.orderAssignmentService.unassignOrder(
      orderId,
      driverId, // Driver is unassigning themselves
      reason,
    );

    // Update the order status to Pending if it was Assigned
    if (order.status === BackendOrderStatus.Assigned) {
      await this.ordersService.changeStatus(
        driver.tenantId,
        orderId,
        driverId,
        BackendOrderStatus.Pending,
        reason || 'Driver rejected order',
        '',
      );
    }

    // Add rejection reason to order notes
    await this.ordersService.update(driver.tenantId, orderId, driverId, {
      internalNotes: `Order rejected by driver: ${reason}`,
    });

    // Return the updated order
    return this.ordersService.findOne(driver.tenantId, orderId);
  }

  async transferOrder(
    driverId: string,
    orderId: string,
    newDriverId: string,
    reason: string,
  ) {
    // Get the driver to determine their tenant ID
    const driver = await this.usersService.findById(driverId);

    if (!driver) {
      throw new AppException(
        `Driver with ID ${driverId} not found`,
        ErrorCode.USER_NOT_FOUND,
        HttpStatus.NOT_FOUND,
      );
    }

    const order = await this.getDriverOrderById(driverId, orderId);

    // Convert backend status to mobile status for comparison
    const mobileStatus = mapBackendToMobileStatus(order.status);
    if (
      ![MobileOrderStatus.Assigned, MobileOrderStatus.Pending].includes(
        mobileStatus,
      )
    ) {
      throw new AppException(
        `Cannot transfer an order that is already ${order.status}`,
        ErrorCode.ORDER_INVALID_STATUS_TRANSITION,
        HttpStatus.BAD_REQUEST,
      );
    }

    // Verify the new driver exists
    const newDriver = await this.usersService.findById(newDriverId);
    if (!newDriver) {
      throw new AppException(
        `New driver with ID ${newDriverId} not found`,
        ErrorCode.USER_NOT_FOUND,
        HttpStatus.NOT_FOUND,
      );
    }

    // Verify both drivers belong to the same tenant
    if (driver.tenantId !== newDriver.tenantId) {
      throw new AppException(
        `Cannot transfer order to driver from different tenant`,
        ErrorCode.USER_INVALID_STATUS,
        HttpStatus.BAD_REQUEST,
      );
    }

    // Check if the new driver is available
    // In a real implementation, you might check driver status, current load, etc.

    // Find the new driver's active vehicle
    const filter = new BaseFilterDto();
    filter.pageNumber = 1;
    filter.pageSize = 10;
    filter.where = {
      driverId: { [FilterOperator.EQ]: newDriverId },
      isActive: { [FilterOperator.EQ]: true },
    };
    const vehiclesResult = await this.vehiclesService.getVehicleList(
      filter,
      driver.tenantId,
    );
    const newVehicleId =
      vehiclesResult.data.length > 0 ? vehiclesResult.data[0].id : undefined;

    // If no active vehicle found and order seems to need a vehicle
    // Since we can't directly check vehicleTypeId in the DTO,
    // we'll make a determination based on other factors
    if (
      !newVehicleId &&
      (order.assignedVehicleId ||
        (order.totalWeight && order.totalWeight > 100))
    ) {
      throw new AppException(
        `New driver has no active vehicle, but this order likely requires one`,
        ErrorCode.VEHICLE_NOT_FOUND,
        HttpStatus.BAD_REQUEST,
      );
    }

    // Reassign the order to the new driver
    await this.orderAssignmentService.reassignOrder(
      orderId,
      driverId, // Current driver is reassigning
      newDriverId,
      newVehicleId,
      reason || 'Driver requested transfer',
    );

    // Add transfer information to order notes
    await this.ordersService.update(driver.tenantId, orderId, driverId, {
      internalNotes: `Order transferred from driver ${driverId} to driver ${newDriverId}. Reason: ${reason}`,
    });

    // Return the updated order
    return this.ordersService.findOne(driver.tenantId, orderId);
  }
}
