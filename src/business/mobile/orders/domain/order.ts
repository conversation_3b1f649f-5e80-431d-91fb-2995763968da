import { AutoMap } from '@automapper/classes';
import { OrderStatus as BackendOrderStatus } from '@app/business/order/orders/domain/order.types';

// Mobile-specific order statuses that map to backend statuses
export enum OrderStatus {
  Pending = 'Pending',
  Assigned = 'Assigned',
  InTransit = 'InTransit',
  OnHold = 'OnHold',
  Delivered = 'Delivered',
  Failed = 'Failed',
  Cancelled = 'Cancelled',
  Returned = 'Returned',
  Completed = 'Completed',
}

// Mapping between mobile and backend order statuses
export const mapMobileToBackendStatus = (
  status: OrderStatus,
): BackendOrderStatus => {
  const statusMap: Record<OrderStatus, BackendOrderStatus> = {
    [OrderStatus.Pending]: BackendOrderStatus.Pending,
    [OrderStatus.Assigned]: BackendOrderStatus.Assigned,
    [OrderStatus.InTransit]: BackendOrderStatus.InProgress,
    [OrderStatus.OnHold]: BackendOrderStatus.Assigned, // No direct mapping
    [OrderStatus.Delivered]: BackendOrderStatus.Completed,
    [OrderStatus.Failed]: BackendOrderStatus.Cancelled,
    [OrderStatus.Cancelled]: BackendOrderStatus.Cancelled,
    [OrderStatus.Returned]: BackendOrderStatus.Cancelled, // No direct mapping
    [OrderStatus.Completed]: BackendOrderStatus.Completed,
  };

  return statusMap[status] || BackendOrderStatus.Pending;
};

// Mapping between backend and mobile order statuses
export const mapBackendToMobileStatus = (
  status: BackendOrderStatus,
): OrderStatus => {
  const statusMap: Record<BackendOrderStatus, OrderStatus> = {
    [BackendOrderStatus.Draft]: OrderStatus.Pending,
    [BackendOrderStatus.Submitted]: OrderStatus.Pending,
    [BackendOrderStatus.Pending]: OrderStatus.Pending,
    [BackendOrderStatus.Assigned]: OrderStatus.Assigned,
    [BackendOrderStatus.InProgress]: OrderStatus.InTransit,
    [BackendOrderStatus.Completed]: OrderStatus.Completed,
    [BackendOrderStatus.Cancelled]: OrderStatus.Cancelled,
  };

  return statusMap[status] || OrderStatus.Pending;
};

export enum OrderPriority {
  Low = 'Low',
  Normal = 'Normal',
  High = 'High',
  Urgent = 'Urgent',
}

export class Location {
  @AutoMap()
  addressLine1: string;

  @AutoMap()
  addressLine2?: string;

  @AutoMap()
  city: string;

  @AutoMap()
  province: string;

  @AutoMap()
  postalCode: string;

  @AutoMap()
  country: string;

  @AutoMap()
  latitude?: number;

  @AutoMap()
  longitude?: number;

  @AutoMap()
  notes?: string;
}

export class OrderItemDomain {
  @AutoMap()
  id: string;

  @AutoMap()
  orderId: string;

  @AutoMap()
  description: string;

  @AutoMap()
  quantity: number;

  @AutoMap()
  weight?: number;

  @AutoMap()
  dimensions?: {
    length: number;
    width: number;
    height: number;
  };

  @AutoMap()
  packageType: string;

  @AutoMap()
  specialInstructions?: string;
}

export class OrderDomain {
  @AutoMap()
  id: string;

  @AutoMap()
  orderNumber: string;

  @AutoMap()
  trackingNumber?: string;

  @AutoMap()
  customerId: string;

  @AutoMap()
  customerName: string;

  @AutoMap()
  requestedByName?: string;

  @AutoMap()
  driverId?: string;

  @AutoMap()
  assignedDriverId?: string;

  @AutoMap()
  vehicleId?: string;

  @AutoMap()
  assignedVehicleId?: string;

  @AutoMap()
  status: OrderStatus;

  @AutoMap()
  priority: OrderPriority;

  @AutoMap()
  pickupLocation?: Location;

  @AutoMap()
  deliveryLocation?: Location;

  @AutoMap()
  collectionAddressSummary?: string;

  @AutoMap()
  deliveryAddressSummary?: string;

  @AutoMap()
  collectionContactName?: string;

  @AutoMap()
  deliveryContactName?: string;

  @AutoMap()
  scheduledPickupTime?: Date;

  @AutoMap()
  scheduledDeliveryTime?: Date;

  @AutoMap()
  scheduledCollectionTime?: Date;

  @AutoMap()
  actualPickupTime?: Date;

  @AutoMap()
  actualDeliveryTime?: Date;

  @AutoMap()
  items: OrderItemDomain[];

  @AutoMap()
  totalPrice: number;

  @AutoMap()
  paymentStatus: string;

  @AutoMap()
  specialInstructions?: string;

  @AutoMap()
  description?: string;

  @AutoMap()
  customerSignature?: string;

  @AutoMap()
  proofOfDeliveryImages?: string[];

  @AutoMap()
  isDeleted: boolean;

  @AutoMap()
  deletedAt?: Date;

  @AutoMap()
  createdAt: Date;

  @AutoMap()
  updatedAt: Date;
}
