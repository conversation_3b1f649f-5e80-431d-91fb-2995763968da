import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { LocationDto } from '@app/business/mobile/timeclock/dto/clock-in.dto';
import { IsNumber, IsOptional, IsString } from 'class-validator';

export class ClockOutDto {
  @ApiProperty({ type: LocationDto })
  location: LocationDto;

  @ApiPropertyOptional({ example: 12550 })
  @IsNumber()
  @IsOptional()
  odometer?: number;

  @ApiPropertyOptional({ example: 'End of shift' })
  @IsString()
  @IsOptional()
  notes?: string;

  @ApiPropertyOptional({ type: 'string', format: 'binary' })
  @IsOptional()
  photo?: any;
}
