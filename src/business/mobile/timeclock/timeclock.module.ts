import { Module } from '@nestjs/common';
import { MobileTimeClockController } from './timeclock.controller';
import { MobileTimeClockService } from './timeclock.service';
import { TimeClockStorage } from './storage/time-clock.storage';
import { MobileAuthModule } from '../auth/auth.module';

@Module({
  imports: [MobileAuthModule],
  controllers: [MobileTimeClockController],
  providers: [MobileTimeClockService, TimeClockStorage],
  exports: [MobileTimeClockService],
})
export class MobileTimeClockModule {}
