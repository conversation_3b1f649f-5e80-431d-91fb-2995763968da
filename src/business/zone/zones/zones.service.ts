import { Injectable } from '@nestjs/common';
import { ZoneRepository } from './infrastructure/repositories/zone.repository';
import { ZoneDomain } from './domain/zone';
import { ZoneTableRepository } from '../zone-tables/infrastructure/repositories/zone-table.repository';
import {
  ZoneNotFoundException,
  ZoneAlreadyExistsException,
  ZonePostalCodeNotFoundException,
} from '@app/utils/errors/exceptions/zone-exceptions';
import { PaginatedResult } from '../../../utils/query-creator/interfaces';
import { BaseFilterDto } from '../../../core/infrastructure/filtering/dtos/base-filter.dto';

@Injectable()
export class ZonesService {
  constructor(
    private readonly zoneRepository: ZoneRepository,
    private readonly zoneTableRepository: ZoneTableRepository,
  ) {}

  async create(zoneDomain: ZoneDomain): Promise<ZoneDomain> {
    const existingZoneByName = await this.zoneRepository.findOne({
      name: zoneDomain.name,
    });
    if (existingZoneByName) {
      throw new ZoneAlreadyExistsException(zoneDomain.name);
    }

    const zone = await this.zoneRepository.create(zoneDomain);
    return zone;
  }

  async getZoneList(
    filter: BaseFilterDto,
    tenantId: string,
  ): Promise<PaginatedResult<ZoneDomain>> {
    const zoneDomain = await this.zoneRepository.find(filter, tenantId);
    return zoneDomain;
  }

  /**
   * Get all zones for a tenant without pagination or filtering
   * @param tenantId The tenant ID
   * @returns Array of zone domains
   */
  async getAllZonesByTenant(tenantId: string): Promise<ZoneDomain[]> {
    return this.zoneRepository.findAllByTenant(tenantId);
  }

  async getZoneDetails(zoneId: ZoneDomain['id']): Promise<ZoneDomain> {
    const zoneDomain = await this.zoneRepository.findOne({
      id: zoneId,
    });
    if (!zoneDomain) {
      throw new ZoneNotFoundException(zoneId);
    }
    return zoneDomain;
  }

  async getZoneByPostalCode(
    postalCode: string,
    tenantId: string,
  ): Promise<ZoneDomain> {
    const zoneDomain =
      await this.zoneRepository.findLatestUpdatedZoneByPostalCode(
        postalCode,
        tenantId,
      );
    if (!zoneDomain) {
      throw new ZonePostalCodeNotFoundException(postalCode);
    }
    return zoneDomain;
  }

  async updateZoneDetails(zoneDomain: ZoneDomain): Promise<void> {
    const existingZone = await this.zoneRepository.findOne({
      id: zoneDomain.id,
    });
    if (!existingZone) {
      throw new ZoneNotFoundException(zoneDomain.id);
    }

    // Check name uniqueness if it's being changed
    if (existingZone.name !== zoneDomain.name) {
      const zoneWithSameName = await this.zoneRepository.findOne({
        name: zoneDomain.name,
      });
      if (zoneWithSameName && zoneWithSameName.id !== zoneDomain.id) {
        throw new ZoneAlreadyExistsException(zoneDomain.name);
      }
    }

    await this.zoneRepository.update(zoneDomain);
    return;
  }

  async deleteZone(zoneId: ZoneDomain['id']): Promise<void> {
    const zone = await this.zoneRepository.findOne({ id: zoneId });
    if (!zone) {
      throw new ZoneNotFoundException(zoneId);
    }

    zone.isDeleted = true;
    zone.deletedAt = new Date();
    await this.zoneRepository.update(zone);

    await this.zoneTableRepository.deleteByZoneId(zoneId);
    return;
  }
}
