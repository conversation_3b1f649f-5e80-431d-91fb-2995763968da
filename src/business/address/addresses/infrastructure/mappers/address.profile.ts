import { createMap, forMember, mapFrom, Mapper } from '@automapper/core';
import { AutomapperProfile, InjectMapper } from '@automapper/nestjs';
import { Injectable } from '@nestjs/common';
import {
  CreateAddressDto,
  UpdateAddressDto,
} from '../../dto/create-address.dto';
import { AddressEntity } from '../entities/address.entity';
import { AddressDomain } from '../../domain/address';
import { GetAddressDto } from '../../dto/get-address.dto';

@Injectable()
export class AddressProfile extends AutomapperProfile {
  constructor(@InjectMapper() mapper: Mapper) {
    super(mapper);
  }

  override get profile() {
    return (mapper: Mapper) => {
      createMap(mapper, CreateAddressDto, AddressDomain);

      createMap(mapper, UpdateAddressDto, AddressDomain);

      createMap(mapper, AddressDomain, AddressEntity);

      createMap(mapper, AddressEntity, AddressDomain);

      createMap(
        mapper,
        AddressDomain,
        GetAddressDto,
        forMember(
          (dest) => dest.customer,
          mapFrom(
            (src) =>
              `${src.customer.companyName} - ${src.customer.contactName}`,
          ),
        ),
      );

      // Custom mapping for duplicate address
      createMap(
        mapper,
        AddressDomain,
        AddressDomain,
        forMember(
          (dest) => dest.id,
          mapFrom(() => undefined),
        ),
        forMember(
          (dest) => dest.name,
          mapFrom((src) => `${src.name} - Copy`),
        ),
        forMember(
          (dest) => dest.companyName,
          mapFrom((src) => `${src.companyName} - Copy`),
        ),
        forMember(
          (dest) => dest.isDefaultForPickup,
          mapFrom(() => undefined),
        ),
        forMember(
          (dest) => dest.isDefaultForDelivery,
          mapFrom(() => undefined),
        ),
        forMember(
          (dest) => dest.isFavoriteForPickup,
          mapFrom(() => undefined),
        ),
        forMember(
          (dest) => dest.isDefaultForDelivery,
          mapFrom(() => undefined),
        ),
        forMember(
          (dest) => dest.createdAt,
          mapFrom(() => undefined),
        ),
        forMember(
          (dest) => dest.updatedAt,
          mapFrom(() => undefined),
        ),
        forMember(
          (dest) => dest.createdBy,
          mapFrom(() => undefined),
        ),
        forMember(
          (dest) => dest.updatedBy,
          mapFrom(() => undefined),
        ),
      );
    };
  }
}
