import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AddressRepository } from './repositories/address.repository';
import { AddressEntity } from './entities/address.entity';
import { UserEntity } from '../../../user/users/infrastructure/entities/user.entity';
import { SecureFilterService } from '../../../../core/infrastructure/filtering/services/secure-filter.service';
import { AddressFilterConfig } from '../address-filter.config';

@Module({
  imports: [TypeOrmModule.forFeature([AddressEntity, UserEntity])],
  providers: [
    AddressRepository,
    {
      provide: SecureFilterService,
      useFactory: () => new SecureFilterService(AddressFilterConfig()),
    },
  ],
  exports: [AddressRepository],
})
export class RelationalAddressPersistenceModule {}
