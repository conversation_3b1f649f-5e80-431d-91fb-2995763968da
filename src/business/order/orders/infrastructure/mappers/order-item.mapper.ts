import { OrderItem } from '../../domain/order-item';
import { OrderItemEntity } from '../entities/order-item.entity';
import { OrderItemResponseDto } from '../../dto/order-item-response.dto';

export class OrderItemMapper {
  static toDomain(entity: OrderItemEntity): OrderItem {
    const domain = new OrderItem();
    domain.id = entity.id;
    domain.orderId = entity.orderId;
    domain.packageTemplateId = entity.packageTemplateId;
    domain.itemType = entity.itemType;
    domain.quantity = entity.quantity;
    domain.weight = entity.weight;
    domain.weightUnit = entity.weightUnit;
    domain.length = entity.length;
    domain.width = entity.width;
    domain.height = entity.height;
    domain.dimensionUnit = entity.dimensionUnit;
    domain.volume = entity.volume;
    domain.declaredValue = entity.declaredValue;
    domain.description = entity.description;
    domain.notes = entity.notes;
    domain.imageUrl = entity.imageUrl;
    domain.metadata = entity.metadata;
    domain.createdAt = entity.createdAt;
    domain.updatedAt = entity.updatedAt;
    return domain;
  }

  static toEntity(domain: Partial<OrderItem>): Partial<OrderItemEntity> {
    const entity = new OrderItemEntity();
    if (domain.id) entity.id = domain.id;
    if (domain.orderId) entity.orderId = domain.orderId;
    if (domain.packageTemplateId !== undefined)
      entity.packageTemplateId = domain.packageTemplateId;
    if (domain.itemType) entity.itemType = domain.itemType;
    if (domain.quantity) entity.quantity = domain.quantity;
    if (domain.weight !== undefined) entity.weight = domain.weight;
    if (domain.weightUnit) entity.weightUnit = domain.weightUnit;
    if (domain.length !== undefined) entity.length = domain.length;
    if (domain.width !== undefined) entity.width = domain.width;
    if (domain.height !== undefined) entity.height = domain.height;
    if (domain.dimensionUnit) entity.dimensionUnit = domain.dimensionUnit;
    if (domain.declaredValue !== undefined)
      entity.declaredValue = domain.declaredValue;
    if (domain.description !== undefined)
      entity.description = domain.description;
    if (domain.notes !== undefined) entity.notes = domain.notes;
    if (domain.imageUrl !== undefined) entity.imageUrl = domain.imageUrl;
    if (domain.metadata !== undefined) entity.metadata = domain.metadata;
    return entity;
  }

  static toResponseDto(domain: OrderItem): OrderItemResponseDto {
    const dto = new OrderItemResponseDto();
    dto.id = domain.id;
    dto.orderId = domain.orderId;
    dto.packageTemplateId = domain.packageTemplateId;
    dto.itemType = domain.itemType;
    dto.quantity = domain.quantity;
    dto.weight = domain.weight;
    dto.weightUnit = domain.weightUnit;
    dto.length = domain.length;
    dto.width = domain.width;
    dto.height = domain.height;
    dto.dimensionUnit = domain.dimensionUnit;
    dto.volume = domain.volume;
    dto.declaredValue = domain.declaredValue;
    dto.description = domain.description;
    dto.notes = domain.notes;
    dto.imageUrl = domain.imageUrl;
    dto.createdAt = domain.createdAt;
    dto.updatedAt = domain.updatedAt;
    return dto;
  }
}
