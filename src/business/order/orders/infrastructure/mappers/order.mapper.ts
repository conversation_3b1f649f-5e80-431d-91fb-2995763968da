import { Order } from '../../domain/order';
import { OrderEntity } from '../entities/order.entity';

export class OrderMapper {
  static toDomain(entity: OrderEntity): Order {
    const domain = new Order();
    domain.id = entity.id;
    domain.tenantId = entity.tenantId;
    domain.trackingNumber = entity.trackingNumber;
    domain.referenceNumber = entity.referenceNumber;
    domain.status = entity.status;

    // Customer Information
    domain.customerId = entity.customerId;
    domain.requestedById = entity.requestedById;
    domain.submittedById = entity.submittedById;

    // Package Template Info
    domain.packageTemplateId = entity.packageTemplateId;

    // Collection Information
    domain.collectionAddressId = entity.collectionAddressId;
    domain.collectionContactName = entity.collectionContactName;
    domain.collectionInstructions = entity.collectionInstructions;
    domain.collectionSignatureRequired = entity.collectionSignatureRequired;
    domain.collectionSignatureData = entity.collectionSignatureData;
    domain.scheduledCollectionTime = entity.scheduledCollectionTime;
    domain.actualCollectionTime = entity.actualCollectionTime;
    domain.collectionZoneId = entity.collectionZoneId;

    // Delivery Information
    domain.deliveryAddressId = entity.deliveryAddressId;
    domain.deliveryContactName = entity.deliveryContactName;
    domain.deliveryInstructions = entity.deliveryInstructions;
    domain.deliverySignatureRequired = entity.deliverySignatureRequired;
    domain.deliverySignatureData = entity.deliverySignatureData;
    domain.scheduledDeliveryTime = entity.scheduledDeliveryTime;
    domain.actualDeliveryTime = entity.actualDeliveryTime;
    domain.deliveryZoneId = entity.deliveryZoneId;

    // Package Information
    domain.totalItems = entity.totalItems;
    domain.totalWeight = entity.totalWeight;
    domain.totalVolume = entity.totalVolume;
    domain.declaredValue = entity.declaredValue;

    // Vehicle and Assignment Information
    domain.vehicleTypeId = entity.vehicleTypeId;
    domain.assignedDriverId = entity.assignedDriverId;
    domain.assignedVehicleId = entity.assignedVehicleId;
    domain.codAmount = entity.codAmount;
    domain.codCollected = entity.codCollected;
    domain.codCollectionDate = entity.codCollectionDate;

    // Pricing Information
    domain.priceSetId = entity.priceSetId;
    domain.basePriceType = entity.basePriceType;
    domain.basePrice = entity.basePrice;
    domain.optionsPrice = entity.optionsPrice;
    domain.miscAdjustment = entity.miscAdjustment;
    domain.customerAdjustment = entity.customerAdjustment;
    domain.totalPrice = entity.totalPrice;

    // Billing Information
    domain.billingStatus = entity.billingStatus;
    domain.paymentStatus = entity.paymentStatus;

    // Stats/Metrics
    domain.distance = entity.distance;
    domain.distanceUnit = entity.distanceUnit;
    domain.estimatedDuration = entity.estimatedDuration;
    domain.actualDuration = entity.actualDuration;

    // Additional Fields
    domain.description = entity.description;
    domain.comments = entity.comments;
    domain.internalNotes = entity.internalNotes;
    domain.customFields = entity.customFields;
    domain.metadata = entity.metadata;

    // System Fields
    domain.isLocked = entity.isLocked;
    domain.lockedBy = entity.lockedBy;
    domain.lockReason = entity.lockReason;
    domain.lockTimestamp = entity.lockTimestamp;
    domain.isDeleted = entity.isDeleted;
    domain.deletedAt = entity.deletedAt;
    domain.createdAt = entity.createdAt;
    domain.updatedAt = entity.updatedAt;
    domain.createdBy = entity.createdBy;
    domain.updatedBy = entity.updatedBy;

    return domain;
  }

  static toEntity(domain: Partial<Order>): Partial<OrderEntity> {
    const entity = new OrderEntity();

    if (domain.id) entity.id = domain.id;
    if (domain.tenantId) entity.tenantId = domain.tenantId;
    if (domain.trackingNumber) entity.trackingNumber = domain.trackingNumber;
    if (domain.referenceNumber !== undefined)
      entity.referenceNumber = domain.referenceNumber;
    if (domain.status) entity.status = domain.status;

    // Customer Information
    if (domain.customerId) entity.customerId = domain.customerId;
    if (domain.requestedById) entity.requestedById = domain.requestedById;
    if (domain.submittedById) entity.submittedById = domain.submittedById;

    // Package Template Info
    if (domain.packageTemplateId !== undefined)
      entity.packageTemplateId = domain.packageTemplateId;

    // Collection Information
    if (domain.collectionAddressId)
      entity.collectionAddressId = domain.collectionAddressId;
    if (domain.collectionContactName !== undefined)
      entity.collectionContactName = domain.collectionContactName;
    if (domain.collectionInstructions !== undefined)
      entity.collectionInstructions = domain.collectionInstructions;
    if (domain.collectionSignatureRequired !== undefined)
      entity.collectionSignatureRequired = domain.collectionSignatureRequired;
    if (domain.collectionSignatureData !== undefined)
      entity.collectionSignatureData = domain.collectionSignatureData;
    if (domain.scheduledCollectionTime !== undefined)
      entity.scheduledCollectionTime = domain.scheduledCollectionTime;
    if (domain.actualCollectionTime !== undefined)
      entity.actualCollectionTime = domain.actualCollectionTime;
    if (domain.collectionZoneId !== undefined)
      entity.collectionZoneId = domain.collectionZoneId;

    // Delivery Information
    if (domain.deliveryAddressId)
      entity.deliveryAddressId = domain.deliveryAddressId;
    if (domain.deliveryContactName !== undefined)
      entity.deliveryContactName = domain.deliveryContactName;
    if (domain.deliveryInstructions !== undefined)
      entity.deliveryInstructions = domain.deliveryInstructions;
    if (domain.deliverySignatureRequired !== undefined)
      entity.deliverySignatureRequired = domain.deliverySignatureRequired;
    if (domain.deliverySignatureData !== undefined)
      entity.deliverySignatureData = domain.deliverySignatureData;
    if (domain.scheduledDeliveryTime !== undefined)
      entity.scheduledDeliveryTime = domain.scheduledDeliveryTime;
    if (domain.actualDeliveryTime !== undefined)
      entity.actualDeliveryTime = domain.actualDeliveryTime;
    if (domain.deliveryZoneId !== undefined)
      entity.deliveryZoneId = domain.deliveryZoneId;

    // Package Information
    if (domain.totalItems !== undefined) entity.totalItems = domain.totalItems;
    if (domain.totalWeight !== undefined)
      entity.totalWeight = domain.totalWeight;
    if (domain.totalVolume !== undefined)
      entity.totalVolume = domain.totalVolume;
    if (domain.declaredValue !== undefined)
      entity.declaredValue = domain.declaredValue;

    // Vehicle and Assignment Information
    if (domain.vehicleTypeId !== undefined)
      entity.vehicleTypeId = domain.vehicleTypeId;
    if (domain.assignedDriverId !== undefined)
      entity.assignedDriverId = domain.assignedDriverId;
    if (domain.assignedVehicleId !== undefined)
      entity.assignedVehicleId = domain.assignedVehicleId;
    if (domain.codAmount !== undefined) entity.codAmount = domain.codAmount;
    if (domain.codCollected !== undefined)
      entity.codCollected = domain.codCollected;
    if (domain.codCollectionDate !== undefined)
      entity.codCollectionDate = domain.codCollectionDate;

    // Pricing Information
    if (domain.priceSetId !== undefined) entity.priceSetId = domain.priceSetId;
    if (domain.basePriceType !== undefined)
      entity.basePriceType = domain.basePriceType;
    if (domain.basePrice !== undefined) entity.basePrice = domain.basePrice;
    if (domain.optionsPrice !== undefined)
      entity.optionsPrice = domain.optionsPrice;
    if (domain.miscAdjustment !== undefined)
      entity.miscAdjustment = domain.miscAdjustment;
    if (domain.customerAdjustment !== undefined)
      entity.customerAdjustment = domain.customerAdjustment;

    // Billing Information
    if (domain.billingStatus) entity.billingStatus = domain.billingStatus;
    if (domain.paymentStatus) entity.paymentStatus = domain.paymentStatus;

    // Stats/Metrics
    if (domain.distance !== undefined) entity.distance = domain.distance;
    if (domain.distanceUnit) entity.distanceUnit = domain.distanceUnit;
    if (domain.estimatedDuration !== undefined)
      entity.estimatedDuration = domain.estimatedDuration;
    if (domain.actualDuration !== undefined)
      entity.actualDuration = domain.actualDuration;

    // Additional Fields
    if (domain.description !== undefined)
      entity.description = domain.description;
    if (domain.comments !== undefined) entity.comments = domain.comments;
    if (domain.internalNotes !== undefined)
      entity.internalNotes = domain.internalNotes;
    if (domain.customFields !== undefined)
      entity.customFields = domain.customFields;
    if (domain.metadata !== undefined) entity.metadata = domain.metadata;

    // System Fields
    if (domain.isLocked !== undefined) entity.isLocked = domain.isLocked;
    if (domain.lockedBy !== undefined) entity.lockedBy = domain.lockedBy;
    if (domain.lockReason !== undefined) entity.lockReason = domain.lockReason;
    if (domain.lockTimestamp !== undefined)
      entity.lockTimestamp = domain.lockTimestamp;
    if (domain.isDeleted !== undefined) entity.isDeleted = domain.isDeleted;
    if (domain.deletedAt !== undefined) entity.deletedAt = domain.deletedAt;
    if (domain.createdAt) entity.createdAt = domain.createdAt;
    if (domain.updatedAt) entity.updatedAt = domain.updatedAt;
    if (domain.createdBy) entity.createdBy = domain.createdBy;
    if (domain.updatedBy !== undefined) entity.updatedBy = domain.updatedBy;

    return entity;
  }
}
