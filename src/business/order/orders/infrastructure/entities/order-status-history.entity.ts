import {
  <PERSON>um<PERSON>,
  CreateDate<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { EntityRelationalHelper } from '@utils/relational-entity-helper';
import { AutoMap } from '@automapper/classes';
import { OrderEntity } from './order.entity';
import { UserEntity } from '@app/business/user/users/infrastructure/entities/user.entity';
import { OrderStatus } from '../../domain/order.types';

@Entity('order_status_history')
export class OrderStatusHistoryEntity extends EntityRelationalHelper {
  @AutoMap()
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @AutoMap()
  @Column('uuid')
  orderId: string;

  @ManyToOne(() => OrderEntity, (order) => order.statusHistory, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'order_id' })
  order: OrderEntity;

  @AutoMap()
  @Column({
    type: 'enum',
    enum: OrderStatus,
    nullable: true,
  })
  previousStatus: OrderStatus | null;

  @AutoMap()
  @Column({
    type: 'enum',
    enum: OrderStatus,
  })
  newStatus: OrderStatus;

  @AutoMap()
  @Column({ length: 255, nullable: true })
  reason: string;

  @AutoMap()
  @Column({ type: 'text', nullable: true })
  comments: string;

  @AutoMap()
  @Column({ type: 'jsonb', nullable: true })
  locationData: Record<string, any>;

  @AutoMap()
  @Column({ type: 'jsonb', default: '{}' })
  metadata: Record<string, any>;

  @AutoMap()
  @Column('uuid')
  changedBy: string;

  @ManyToOne(() => UserEntity)
  @JoinColumn({ name: 'changed_by' })
  changedByUser: UserEntity;

  @AutoMap()
  @CreateDateColumn({ type: 'timestamptz' })
  changedAt: Date;
}
