import {
  <PERSON>um<PERSON>,
  CreateDate<PERSON><PERSON>umn,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>um<PERSON>,
  ManyTo<PERSON>ne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { EntityRelationalHelper } from '@utils/relational-entity-helper';
import { AutoMap } from '@automapper/classes';
import { OrderEntity } from './order.entity';
import { UserEntity } from '@app/business/user/users/infrastructure/entities/user.entity';
import { VehicleEntity } from '@app/business/vehicle/vehicles/infrastructure/entities/vehicle.entity';

@Entity('order_assignment_history')
export class OrderAssignmentHistoryEntity extends EntityRelationalHelper {
  @AutoMap()
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @AutoMap()
  @Column('uuid')
  orderId: string;

  @ManyToOne(() => OrderEntity, (order) => order.assignmentHistory, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'order_id' })
  order: OrderEntity;

  @AutoMap()
  @Column('uuid', { nullable: true })
  previousAssigneeId: string;

  @ManyToOne(() => UserEntity)
  @JoinColumn({ name: 'previous_assignee_id' })
  previousAssignee: UserEntity;

  @AutoMap()
  @Column('uuid', { nullable: true })
  newAssigneeId: string;

  @ManyToOne(() => UserEntity)
  @JoinColumn({ name: 'new_assignee_id' })
  newAssignee: UserEntity;

  @AutoMap()
  @Column('uuid')
  assignedById: string;

  @ManyToOne(() => UserEntity)
  @JoinColumn({ name: 'assigned_by' })
  assignedBy: UserEntity;

  @AutoMap()
  @Column('uuid', { nullable: true })
  assignedVehicleId: string;

  @ManyToOne(() => VehicleEntity)
  @JoinColumn({ name: 'assigned_vehicle_id' })
  assignedVehicle: VehicleEntity;

  @AutoMap()
  @Column('uuid', { nullable: true })
  previousVehicleId: string;

  @ManyToOne(() => VehicleEntity)
  @JoinColumn({ name: 'previous_vehicle_id' })
  previousVehicle: VehicleEntity;

  @AutoMap()
  @Column({ length: 50 })
  assignmentType: string; // 'Assign', 'Unassign', 'Reassign'

  @AutoMap()
  @Column({ type: 'text', nullable: true })
  reason: string;

  @AutoMap()
  @CreateDateColumn({ type: 'timestamptz' })
  createdAt: Date;

  @AutoMap()
  @Column({ type: 'jsonb', default: '{}' })
  metadata: Record<string, any>;
}
