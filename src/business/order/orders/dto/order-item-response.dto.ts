import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class OrderItemResponseDto {
  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-446655440000',
    description: 'Unique identifier for the order item',
  })
  id: string;

  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-446655441111',
    description: 'ID of the order this item belongs to',
  })
  orderId: string;

  @ApiPropertyOptional({
    example: '550e8400-e29b-41d4-a716-446655442222',
    description: 'Package template ID',
  })
  packageTemplateId?: string;

  @ApiPropertyOptional({
    example: 'Standard Box',
    description: 'Package template name',
  })
  packageTemplateName?: string;

  @ApiProperty({
    example: 'Box',
    description: 'Type of item',
  })
  itemType: string;

  @ApiProperty({
    example: 2,
    description: 'Quantity of this item',
    default: 1,
  })
  quantity: number;

  @ApiPropertyOptional({
    example: 5.5,
    description: 'Weight of this item',
  })
  weight?: number;

  @ApiPropertyOptional({
    example: 'kg',
    description: 'Unit of weight measurement',
    default: 'kg',
  })
  weightUnit?: string;

  @ApiPropertyOptional({
    example: 30,
    description: 'Length of this item',
  })
  length?: number;

  @ApiPropertyOptional({
    example: 20,
    description: 'Width of this item',
  })
  width?: number;

  @ApiPropertyOptional({
    example: 15,
    description: 'Height of this item',
  })
  height?: number;

  @ApiPropertyOptional({
    example: 'cm',
    description: 'Unit of dimension measurement',
    default: 'cm',
  })
  dimensionUnit?: string;

  @ApiPropertyOptional({
    example: 9000,
    description: 'Volume of this item (length × width × height)',
  })
  volume?: number;

  @ApiPropertyOptional({
    example: 200,
    description: 'Declared value of this item',
  })
  declaredValue?: number;

  @ApiPropertyOptional({
    example: 'Fragile electronics equipment',
    description: 'Description of this item',
  })
  description?: string;

  @ApiPropertyOptional({
    example: 'Handle with care',
    description: 'Notes for this item',
  })
  notes?: string;

  @ApiPropertyOptional({
    example: 'https://example.com/items/12345.jpg',
    description: 'URL to an image of this item',
  })
  imageUrl?: string;

  @ApiProperty({
    example: '2025-04-08T12:34:56Z',
    description: 'Creation timestamp',
  })
  createdAt: Date;

  @ApiProperty({
    example: '2025-04-08T14:22:33Z',
    description: 'Last update timestamp',
  })
  updatedAt: Date;
}
