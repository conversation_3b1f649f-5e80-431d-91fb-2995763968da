import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  Is<PERSON>rray,
  IsBoolean,
  IsDate,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  IsUUID,
  <PERSON><PERSON>ength,
  Min,
  MinDate,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { OrderStatus, DistanceUnit } from '../domain/order.types';
import { CreateOrderItemDto } from './create-order-item.dto';

export class CreateOrderDto {
  @ApiPropertyOptional({
    example: 'REF-12345',
    description: 'Optional reference number (customer supplied)',
  })
  @IsString()
  @IsOptional()
  @MaxLength(100)
  referenceNumber?: string;

  @ApiPropertyOptional({
    enum: OrderStatus,
    default: OrderStatus.Draft,
    description: 'Initial status of the order',
  })
  @IsEnum(OrderStatus)
  @IsOptional()
  status?: OrderStatus;

  // Customer Information
  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-************',
    description: 'Customer ID',
  })
  @IsUUID('4')
  @IsNotEmpty()
  customerId: string;

  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-************',
    description: 'Contact who requested the order',
  })
  @IsUUID('4')
  @IsNotEmpty()
  requestedById: string;

  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-************',
    description: 'Contact who submitted the order',
  })
  @IsUUID('4')
  @IsNotEmpty()
  submittedById: string;

  // Package Template Info
  @ApiPropertyOptional({
    example: '550e8400-e29b-41d4-a716-************',
    description: 'Package template ID',
  })
  @IsUUID('4')
  @IsOptional()
  packageTemplateId?: string;

  // Collection Information
  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-************',
    description: 'Collection address ID',
  })
  @IsUUID('4')
  @IsNotEmpty()
  collectionAddressId: string;

  @ApiPropertyOptional({
    example: 'John Smith',
    description: 'Contact name for collection',
  })
  @IsString()
  @IsOptional()
  @MaxLength(255)
  collectionContactName?: string;

  @ApiPropertyOptional({
    example: 'Ring doorbell and wait for security',
    description: 'Special instructions for collection',
  })
  @IsString()
  @IsOptional()
  collectionInstructions?: string;

  @ApiPropertyOptional({
    example: false,
    description: 'Whether signature is required for collection',
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  collectionSignatureRequired?: boolean;

  @ApiPropertyOptional({
    example: '2025-04-10T09:00:00Z',
    description: 'Scheduled collection time',
  })
  @IsDate()
  @Type(() => Date)
  @IsOptional()
  scheduledCollectionTime?: Date;

  @ApiPropertyOptional({
    example: '550e8400-e29b-41d4-a716-************',
    description: 'Collection zone ID',
  })
  @IsUUID('4')
  @IsOptional()
  collectionZoneId?: string;

  // Delivery Information
  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-************',
    description: 'Delivery address ID',
  })
  @IsUUID('4')
  @IsNotEmpty()
  deliveryAddressId: string;

  @ApiPropertyOptional({
    example: 'Jane Smith',
    description: 'Contact name for delivery',
  })
  @IsString()
  @IsOptional()
  @MaxLength(255)
  deliveryContactName?: string;

  @ApiPropertyOptional({
    example: 'Leave at front desk if no answer',
    description: 'Special instructions for delivery',
  })
  @IsString()
  @IsOptional()
  deliveryInstructions?: string;

  @ApiPropertyOptional({
    example: true,
    description: 'Whether signature is required for delivery',
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  deliverySignatureRequired?: boolean;

  @ApiPropertyOptional({
    example: '2025-04-10T14:00:00Z',
    description: 'Scheduled delivery time',
  })
  @IsDate()
  @Type(() => Date)
  @IsOptional()
  @MinDate(new Date()) // Delivery date must be in the future
  scheduledDeliveryTime?: Date;

  @ApiPropertyOptional({
    example: '550e8400-e29b-41d4-a716-************',
    description: 'Delivery zone ID',
  })
  @IsUUID('4')
  @IsOptional()
  deliveryZoneId?: string;

  // Package Information
  @ApiPropertyOptional({
    example: 3,
    description: 'Total number of items',
    default: 1,
  })
  @IsNumber()
  @Min(1)
  @IsOptional()
  totalItems?: number;

  @ApiPropertyOptional({
    example: 12.5,
    description: 'Total weight of all items',
  })
  @IsNumber()
  @Min(0)
  @IsOptional()
  totalWeight?: number;

  @ApiPropertyOptional({
    example: 0.5,
    description: 'Total volume of all items in cubic units',
  })
  @IsNumber()
  @Min(0)
  @IsOptional()
  totalVolume?: number;

  @ApiPropertyOptional({
    example: 500,
    description: 'Declared value of the shipment',
  })
  @IsNumber()
  @Min(0)
  @IsOptional()
  declaredValue?: number;

  // Vehicle and Assignment Information
  @ApiPropertyOptional({
    example: '550e8400-e29b-41d4-a716-************',
    description: 'Vehicle type ID',
  })
  @IsUUID('4')
  @IsOptional()
  vehicleTypeId?: string;

  @ApiPropertyOptional({
    example: '550e8400-e29b-41d4-a716-************',
    description: 'Assigned driver ID',
  })
  @IsUUID('4')
  @IsOptional()
  assignedDriverId?: string;

  @ApiPropertyOptional({
    example: '550e8400-e29b-41d4-a716-************',
    description: 'Assigned vehicle ID',
  })
  @IsUUID('4')
  @IsOptional()
  assignedVehicleId?: string;

  @ApiPropertyOptional({
    example: 100.0,
    description: 'Cash on delivery amount',
  })
  @IsNumber()
  @Min(0)
  @IsOptional()
  codAmount?: number;

  // Pricing Information
  @ApiPropertyOptional({
    example: '550e8400-e29b-41d4-a716-************',
    description: 'Price set ID',
  })
  @IsUUID('4')
  @IsOptional()
  priceSetId?: string;

  @ApiPropertyOptional({
    example: 'Standard',
    description: 'Base price type',
  })
  @IsString()
  @IsOptional()
  @MaxLength(50)
  basePriceType?: string;

  // Stats/Metrics
  @ApiPropertyOptional({
    example: 10.5,
    description: 'Distance in selected units',
  })
  @IsNumber()
  @Min(0)
  @IsOptional()
  distance?: number;

  @ApiPropertyOptional({
    enum: DistanceUnit,
    default: DistanceUnit.Kilometers,
    description: 'Unit of distance measurement',
  })
  @IsEnum(DistanceUnit)
  @IsOptional()
  distanceUnit?: DistanceUnit;

  // Additional Fields
  @ApiPropertyOptional({
    example: 'Urgent delivery of medical supplies',
    description: 'Order description',
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional({
    example: 'Customer requested expedited shipping',
    description: 'Comments about the order',
  })
  @IsString()
  @IsOptional()
  comments?: string;

  @ApiPropertyOptional({
    example: 'VIP customer - handle with care',
    description: 'Internal notes (not visible to customer)',
  })
  @IsString()
  @IsOptional()
  internalNotes?: string;

  @ApiPropertyOptional({
    type: [CreateOrderItemDto],
    description: 'Items to include in this order',
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateOrderItemDto)
  @IsOptional()
  items?: CreateOrderItemDto[];

  @ApiPropertyOptional({
    example: {
      department: 'Electronics',
      priority: 'High',
    },
    description: 'Custom fields',
  })
  @IsObject()
  @IsOptional()
  customFields?: Record<string, any>;

  @ApiPropertyOptional({
    example: {
      originalRequestId: '12345',
      customerSystem: 'ERP-XYZ',
    },
    description: 'Additional metadata',
  })
  @IsObject()
  @IsOptional()
  metadata?: Record<string, any>;
}
