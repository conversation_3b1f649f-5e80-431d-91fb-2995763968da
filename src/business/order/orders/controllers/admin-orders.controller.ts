import {
  Controller,
  Get,
  Param,
  Query,
  UseGuards,
  Req,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiOkResponse,
  ApiParam,
  ApiBearerAuth,
  ApiCookieAuth,
  ApiNotFoundResponse,
} from '@nestjs/swagger';
import { Request } from 'express';
import { JwtAuthGuard } from '@core/auth/guards/jwt-auth.guard';
import { OrdersService } from '../orders.service';
import { OrderDetailResponseDto } from '../dto/order-detail-response.dto';
import { FindAllOrdersResponseDto } from '../dto/find-all-orders-response.dto';
import { BaseFilterDto } from '@core/infrastructure/filtering/dtos/base-filter.dto';
import { SecureFilterService } from '@core/infrastructure/filtering/services/secure-filter.service';
import { CurrentUser } from '@core/auth/decorators/current-user.decorator';
import { JwtPayload } from '@core/auth/domain/auth.types';

@ApiTags('Admin - Orders')
@ApiBearerAuth()
@ApiCookieAuth('session_token')
@Controller({
  path: '/admin/orders',
  version: '1',
})
@UseGuards(JwtAuthGuard)
export class AdminOrdersController {
  constructor(
    private readonly ordersService: OrdersService,
    private readonly secureFilterService: SecureFilterService,
  ) {}

  @Get()
  @ApiOperation({ summary: 'Get all orders (admin)' })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'List of orders with pagination info',
    type: FindAllOrdersResponseDto,
  })
  async findAll(
    @CurrentUser() userData: JwtPayload,
    @Req() request: Request,
    @Query() filter: BaseFilterDto,
  ) {
    const combinedFilter = this.secureFilterService.parseKeyOperatorValueQuery(
      request.query,
      filter,
    );

    return this.ordersService.findAll(userData.ctx.tenantId, combinedFilter);
  }

  @Get('customer/:customerId')
  @ApiOperation({ summary: 'Get orders by customer ID (admin)' })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'List of orders for a specific customer with pagination info',
    type: FindAllOrdersResponseDto,
  })
  @ApiParam({
    name: 'customerId',
    description: 'Customer ID',
    type: 'string',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  async findByCustomerId(
    @CurrentUser() userData: JwtPayload,
    @Param('customerId') customerId: string,
    @Req() request: Request,
    @Query() filter: BaseFilterDto,
  ) {
    const combinedFilter = this.secureFilterService.parseKeyOperatorValueQuery(
      request.query,
      filter,
    );

    // Add customer filter
    // Add customer filter to where clause
    combinedFilter.where = combinedFilter.where || {};
    if (
      typeof combinedFilter.where === 'object' &&
      !Array.isArray(combinedFilter.where)
    ) {
      combinedFilter.where['customerId'] =
        combinedFilter.where['customerId'] || {};
      combinedFilter.where['customerId']['eq'] = customerId;
    }

    return this.ordersService.findAll(userData.ctx.tenantId, combinedFilter);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get order by ID (admin)' })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Order details with complete history',
    type: OrderDetailResponseDto,
  })
  @ApiNotFoundResponse({ description: 'Order not found' })
  @ApiParam({
    name: 'id',
    description: 'Order ID',
    type: 'string',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  async findOne(
    @CurrentUser() userData: JwtPayload,
    @Param('id') id: string,
  ): Promise<OrderDetailResponseDto> {
    return this.ordersService.findOne(userData.ctx.tenantId, id);
  }

  @Get('tracking/:trackingNumber')
  @ApiOperation({ summary: 'Get order by tracking number (admin)' })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Order details with complete history',
    type: OrderDetailResponseDto,
  })
  @ApiNotFoundResponse({ description: 'Order not found' })
  @ApiParam({
    name: 'trackingNumber',
    description: 'Order tracking number',
    type: 'string',
    example: 'TRK-20250408-AB12C',
  })
  async findByTrackingNumber(
    @CurrentUser() userData: JwtPayload,
    @Param('trackingNumber') trackingNumber: string,
  ): Promise<OrderDetailResponseDto> {
    return this.ordersService.findByTrackingNumber(
      userData.ctx.tenantId,
      trackingNumber,
    );
  }
}
