import { Injectable } from '@nestjs/common';
import { OrderRepository } from './infrastructure/repositories/order.repository';
import { OrderItemRepository } from './infrastructure/repositories/order-item.repository';
import { OrderStatusHistoryRepository } from './infrastructure/repositories/order-status-history.repository';
import { OrderAssignmentHistoryRepository } from './infrastructure/repositories/order-assignment-history.repository';
import { OrderStopHistoryRepository } from './infrastructure/repositories/order-stop-history.repository';
import { TrackingNumberService } from './services/tracking-number.service';
import { OrderStatusService } from './services/order-status.service';
import { OrderAssignmentService } from './services/order-assignment.service';
import { CreateOrderDto } from './dto/create-order.dto';
import { UpdateOrderDto } from './dto/update-order.dto';
import { OrderResponseDto } from './dto/order-response.dto';
import { OrderDetailResponseDto } from './dto/order-detail-response.dto';
import { CreateOrderItemDto } from './dto/create-order-item.dto';
import { UpdateOrderItemDto } from './dto/update-order-item.dto';
import {
  OrderStatus,
  OrderStopType,
  BillingStatus,
  PaymentStatus,
  DistanceUnit,
} from './domain/order.types';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { DataSource } from 'typeorm';
import { BaseFilterDto } from '@core/infrastructure/filtering/dtos/base-filter.dto';
import { PaginatedResult } from '@utils/query-creator/interfaces';
import { Order } from './domain/order';
import { OrderItem } from './domain/order-item';
import {
  OrderNotFoundException,
  OrderTenantMismatchException,
  OrderLockedException,
  InvalidOrderStatusTransitionException,
  OrderUpdateFailedException,
  OrderCreationFailedException,
  InvalidOrderDataException,
  OrderItemNotFoundException,
  OrderDeliveryTimeInvalidException,
} from '@utils/errors/exceptions/order.exceptions';
import { DraftOrderResponseDto } from '@app/business/order/orders/dto/draft-order-response.dto';
import { DraftOrderDto } from '@app/business/order/orders/dto/draft-order.dto';

@Injectable()
export class OrdersService {
  constructor(
    private readonly orderRepository: OrderRepository,
    private readonly orderItemRepository: OrderItemRepository,
    private readonly orderStatusHistoryRepository: OrderStatusHistoryRepository,
    private readonly orderAssignmentHistoryRepository: OrderAssignmentHistoryRepository,
    private readonly orderStopHistoryRepository: OrderStopHistoryRepository,
    private readonly trackingNumberService: TrackingNumberService,
    private readonly orderStatusService: OrderStatusService,
    private readonly orderAssignmentService: OrderAssignmentService,
    private readonly connection: DataSource,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  /**
   * Create a new order
   */
  async createOrder(
    tenantId: string,
    userId: string,
    createOrderDto: CreateOrderDto,
  ): Promise<OrderResponseDto> {
    // 1. Validate delivery time if provided
    if (
      createOrderDto.scheduledCollectionTime &&
      createOrderDto.scheduledDeliveryTime &&
      createOrderDto.scheduledDeliveryTime <=
        createOrderDto.scheduledCollectionTime
    ) {
      throw new OrderDeliveryTimeInvalidException();
    }

    // DEBUG: Log foreign key references for debugging
    console.log('DEBUG - Order foreign key references:');
    console.log(`Tenant ID: ${tenantId}`);
    console.log(`Customer ID: ${createOrderDto.customerId}`);
    console.log(`Requested By ID: ${createOrderDto.requestedById}`);
    console.log(`Submitted By ID: ${createOrderDto.submittedById}`);
    console.log(
      `Package Template ID: ${createOrderDto.packageTemplateId || 'NULL'}`,
    );
    console.log(`Collection Address ID: ${createOrderDto.collectionAddressId}`);
    console.log(
      `Collection Zone ID: ${createOrderDto.collectionZoneId || 'NULL'}`,
    );
    console.log(`Delivery Address ID: ${createOrderDto.deliveryAddressId}`);
    console.log(`Delivery Zone ID: ${createOrderDto.deliveryZoneId || 'NULL'}`);
    console.log(`Vehicle Type ID: ${createOrderDto.vehicleTypeId || 'NULL'}`);
    console.log(
      `Assigned Driver ID: ${createOrderDto.assignedDriverId || 'NULL (allowed - can be assigned later)'}`,
    );
    console.log(
      `Assigned Vehicle ID: ${createOrderDto.assignedVehicleId || 'NULL'}`,
    );
    console.log(`Price Set ID: ${createOrderDto.priceSetId || 'NULL'}`);

    // 2. Start transaction
    const queryRunner = this.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 3. DEBUG: Verify entity existence before creating order
      // Verify customer exists
      if (createOrderDto.customerId) {
        const customerExists = await queryRunner.manager.query(
          'SELECT EXISTS(SELECT 1 FROM users WHERE id = $1)',
          [createOrderDto.customerId],
        );
        if (!customerExists[0].exists) {
          throw new OrderCreationFailedException(
            `Customer with ID ${createOrderDto.customerId} does not exist`,
          );
        }
      }

      // Verify requestedById exists
      if (createOrderDto.requestedById) {
        const contactExists = await queryRunner.manager.query(
          'SELECT EXISTS(SELECT 1 FROM contacts WHERE id = $1)',
          [createOrderDto.requestedById],
        );
        if (!contactExists[0].exists) {
          throw new OrderCreationFailedException(
            `Contact (requested_by) with ID ${createOrderDto.requestedById} does not exist`,
          );
        }
      }

      // Verify submittedById exists
      if (createOrderDto.submittedById) {
        const contactExists = await queryRunner.manager.query(
          'SELECT EXISTS(SELECT 1 FROM contacts WHERE id = $1)',
          [createOrderDto.submittedById],
        );
        if (!contactExists[0].exists) {
          throw new OrderCreationFailedException(
            `Contact (submitted_by) with ID ${createOrderDto.submittedById} does not exist`,
          );
        }
      }

      // Verify packageTemplateId exists if provided
      if (createOrderDto.packageTemplateId) {
        const packageTemplateExists = await queryRunner.manager.query(
          'SELECT EXISTS(SELECT 1 FROM package_templates WHERE id = $1)',
          [createOrderDto.packageTemplateId],
        );
        if (!packageTemplateExists[0].exists) {
          throw new OrderCreationFailedException(
            `Package template with ID ${createOrderDto.packageTemplateId} does not exist`,
          );
        }
      }

      // Verify collection address exists
      if (createOrderDto.collectionAddressId) {
        const addressExists = await queryRunner.manager.query(
          'SELECT EXISTS(SELECT 1 FROM addresses WHERE id = $1)',
          [createOrderDto.collectionAddressId],
        );
        if (!addressExists[0].exists) {
          throw new OrderCreationFailedException(
            `Collection address with ID ${createOrderDto.collectionAddressId} does not exist`,
          );
        }
      }

      // Verify collection zone exists if provided
      if (createOrderDto.collectionZoneId) {
        const zoneExists = await queryRunner.manager.query(
          'SELECT EXISTS(SELECT 1 FROM zones WHERE id = $1)',
          [createOrderDto.collectionZoneId],
        );
        if (!zoneExists[0].exists) {
          throw new OrderCreationFailedException(
            `Collection zone with ID ${createOrderDto.collectionZoneId} does not exist`,
          );
        }
      }

      // Verify delivery address exists
      if (createOrderDto.deliveryAddressId) {
        const addressExists = await queryRunner.manager.query(
          'SELECT EXISTS(SELECT 1 FROM addresses WHERE id = $1)',
          [createOrderDto.deliveryAddressId],
        );
        if (!addressExists[0].exists) {
          throw new OrderCreationFailedException(
            `Delivery address with ID ${createOrderDto.deliveryAddressId} does not exist`,
          );
        }
      }

      // Verify delivery zone exists if provided
      if (createOrderDto.deliveryZoneId) {
        const zoneExists = await queryRunner.manager.query(
          'SELECT EXISTS(SELECT 1 FROM zones WHERE id = $1)',
          [createOrderDto.deliveryZoneId],
        );
        if (!zoneExists[0].exists) {
          throw new OrderCreationFailedException(
            `Delivery zone with ID ${createOrderDto.deliveryZoneId} does not exist`,
          );
        }
      }

      // Verify vehicle type exists if provided
      if (createOrderDto.vehicleTypeId) {
        const vehicleTypeExists = await queryRunner.manager.query(
          'SELECT EXISTS(SELECT 1 FROM vehicle_types WHERE id = $1)',
          [createOrderDto.vehicleTypeId],
        );
        if (!vehicleTypeExists[0].exists) {
          throw new OrderCreationFailedException(
            `Vehicle type with ID ${createOrderDto.vehicleTypeId} does not exist`,
          );
        }
      }

      // Verify assigned driver exists if provided
      // Note: assignedDriverId is optional and can be empty/null during order creation
      // It can be assigned later during order fulfillment
      if (createOrderDto.assignedDriverId) {
        const driverExists = await queryRunner.manager.query(
          "SELECT EXISTS(SELECT 1 FROM users WHERE id = $1 AND user_type = 'Driver')",
          [createOrderDto.assignedDriverId],
        );
        if (!driverExists[0].exists) {
          throw new OrderCreationFailedException(
            `Driver with ID ${createOrderDto.assignedDriverId} does not exist`,
          );
        }
      }

      // Verify assigned vehicle exists if provided
      if (createOrderDto.assignedVehicleId) {
        const vehicleExists = await queryRunner.manager.query(
          'SELECT EXISTS(SELECT 1 FROM vehicles WHERE id = $1)',
          [createOrderDto.assignedVehicleId],
        );
        if (!vehicleExists[0].exists) {
          throw new OrderCreationFailedException(
            `Vehicle with ID ${createOrderDto.assignedVehicleId} does not exist`,
          );
        }
      }

      // Verify price set exists if provided
      if (createOrderDto.priceSetId) {
        const priceSetExists = await queryRunner.manager.query(
          'SELECT EXISTS(SELECT 1 FROM price_sets WHERE id = $1)',
          [createOrderDto.priceSetId],
        );
        if (!priceSetExists[0].exists) {
          throw new OrderCreationFailedException(
            `Price set with ID ${createOrderDto.priceSetId} does not exist`,
          );
        }
      }

      // 4. Generate tracking number
      const trackingNumber =
        this.trackingNumberService.generateTrackingNumber();

      // 5. Create order entity
      // Note: We allow empty assignedDriverId as it can be assigned later
      const order = new Order();
      order.tenantId = tenantId;
      order.trackingNumber = trackingNumber;
      order.referenceNumber = createOrderDto.referenceNumber;
      order.status = createOrderDto.status || OrderStatus.Draft;
      order.customerId = createOrderDto.customerId;
      order.requestedById = createOrderDto.requestedById;
      order.submittedById = createOrderDto.submittedById;
      order.packageTemplateId = createOrderDto.packageTemplateId;
      order.collectionAddressId = createOrderDto.collectionAddressId;
      order.collectionContactName = createOrderDto.collectionContactName;
      order.collectionInstructions = createOrderDto.collectionInstructions;
      order.collectionSignatureRequired =
        createOrderDto.collectionSignatureRequired ?? false;
      order.scheduledCollectionTime = createOrderDto.scheduledCollectionTime;
      order.collectionZoneId = createOrderDto.collectionZoneId;
      order.deliveryAddressId = createOrderDto.deliveryAddressId;
      order.deliveryContactName = createOrderDto.deliveryContactName;
      order.deliveryInstructions = createOrderDto.deliveryInstructions;
      order.deliverySignatureRequired =
        createOrderDto.deliverySignatureRequired ?? false;
      order.scheduledDeliveryTime = createOrderDto.scheduledDeliveryTime;
      order.deliveryZoneId = createOrderDto.deliveryZoneId;
      order.totalItems = createOrderDto.totalItems ?? 1;
      order.totalWeight = createOrderDto.totalWeight;
      order.totalVolume = createOrderDto.totalVolume;
      order.declaredValue = createOrderDto.declaredValue;
      order.vehicleTypeId = createOrderDto.vehicleTypeId;
      order.assignedDriverId = createOrderDto.assignedDriverId;
      order.assignedVehicleId = createOrderDto.assignedVehicleId;
      order.codAmount = createOrderDto.codAmount;
      order.codCollected = false;
      order.priceSetId = createOrderDto.priceSetId;
      order.basePriceType = createOrderDto.basePriceType;
      order.basePrice = 0; // Will be calculated later
      order.optionsPrice = 0;
      order.miscAdjustment = 0;
      order.customerAdjustment = 0;
      order.billingStatus = BillingStatus.NotBilled;
      order.paymentStatus = PaymentStatus.Pending;
      order.distance = createOrderDto.distance;
      order.distanceUnit =
        createOrderDto.distanceUnit || DistanceUnit.Kilometers;
      order.description = createOrderDto.description;
      order.comments = createOrderDto.comments;
      order.internalNotes = createOrderDto.internalNotes;
      order.customFields = createOrderDto.customFields || {};
      order.metadata = createOrderDto.metadata || {};
      order.isLocked = false;
      order.isDeleted = false;
      order.createdBy = userId;

      // 6. Save order
      console.log(
        'DEBUG - About to create order with the following data:',
        JSON.stringify(order, null, 2),
      );
      const createdOrder = await this.orderRepository.create(order);

      // 6. Create initial status history
      await this.orderStatusHistoryRepository.create(
        createdOrder.id,
        null, // No previous status for new order
        createdOrder.status,
        userId,
        'Order created',
      );

      // 7. Create order items if provided
      if (createOrderDto.items && createOrderDto.items.length > 0) {
        await this.orderItemRepository.createBulk(
          createdOrder.id,
          createOrderDto.items,
        );

        // Update order totals
        const totals = await this.orderItemRepository.calculateOrderTotals(
          createdOrder.id,
        );

        // If we have updated totals, save them
        if (totals) {
          createdOrder.totalItems = totals.totalItems;
          createdOrder.totalWeight = totals.totalWeight;
          createdOrder.totalVolume = totals.totalVolume;
          createdOrder.declaredValue = totals.totalDeclaredValue;

          await this.orderRepository.update(createdOrder.id, tenantId, {
            totalItems: totals.totalItems,
            totalWeight: totals.totalWeight,
            totalVolume: totals.totalVolume,
            declaredValue: totals.totalDeclaredValue,
          });
        }
      }

      // 8. Create order stop history entries for collection and delivery
      if (createOrderDto.scheduledCollectionTime) {
        await this.orderStopHistoryRepository.create(
          createdOrder.id,
          OrderStopType.Collection,
          createOrderDto.scheduledCollectionTime,
          userId,
        );
      }

      if (createOrderDto.scheduledDeliveryTime) {
        await this.orderStopHistoryRepository.create(
          createdOrder.id,
          OrderStopType.Delivery,
          createOrderDto.scheduledDeliveryTime,
          userId,
        );
      }

      // 9. Commit transaction
      await queryRunner.commitTransaction();

      // 10. Emit order created event with enhanced payload for notifications
      // Get data needed for notification templates
      const orderItems = await this.orderItemRepository.findByOrderId(
        createdOrder.id,
      );
      const orderDetails = await this.findOne(tenantId, createdOrder.id);

      this.eventEmitter.emit('order.created', {
        order: {
          ...orderDetails,
          items: orderItems,
          collection: {
            address: 'Collection Address', // This would be populated from the address service
            contactName: createdOrder.collectionContactName,
            contactPhone: '', // This would be populated from the address service
            date: createdOrder.scheduledCollectionTime,
            timeWindow: '9am-12pm', // This would be populated from the real data
          },
          delivery: {
            address: 'Delivery Address', // This would be populated from the address service
            contactName: createdOrder.deliveryContactName,
            contactPhone: '', // This would be populated from the address service
            date: createdOrder.scheduledDeliveryTime,
            timeWindow: '2pm-5pm', // This would be populated from the real data
          },
        },
        tenantId: createdOrder.tenantId,
      });

      // 11. Return order response
      const orderResponse = this.mapOrderToResponseDto(createdOrder);

      return orderResponse;
    } catch (error) {
      // Rollback transaction in case of error
      await queryRunner.rollbackTransaction();

      console.error('DEBUG - Order creation failed with error:', error);

      if (error instanceof OrderDeliveryTimeInvalidException) {
        throw error;
      }

      // Check for FK constraint violation and provide more detailed error
      if (error.message && error.message.includes('foreign key constraint')) {
        console.error(
          'DEBUG - Foreign key constraint violation details:',
          error,
        );

        // Extract constraint name from error if possible
        const constraintMatch = error.message.match(/constraint "([^"]+)"/);
        const constraintName = constraintMatch ? constraintMatch[1] : 'unknown';

        // Provide more detailed error message
        throw new OrderCreationFailedException(
          `Foreign key constraint violation (${constraintName}). One or more referenced entities do not exist.`,
        );
      }

      // For any other error, check if it's a foreign key violation and give better guidance
      if (
        error.message &&
        error.message.includes('violates foreign key constraint')
      ) {
        console.error('DEBUG - Foreign key issue details:', error.message);

        if (error.message.includes('assigned_driver_id')) {
          throw new OrderCreationFailedException(
            'Error with assigned driver ID. This field is optional and can be left empty during order creation.',
          );
        } else {
          throw new OrderCreationFailedException(error.message);
        }
      } else {
        throw new OrderCreationFailedException(error.message);
      }
    } finally {
      // Release query runner
      await queryRunner.release();
    }
  }

  /**
   * Get all orders for a tenant with filtering
   */
  async findAll(
    tenantId: string,
    filter: BaseFilterDto,
  ): Promise<PaginatedResult<OrderResponseDto>> {
    return this.orderRepository.findByTenantId(tenantId, filter);
  }

  /**
   * Get a specific order by ID with detailed information
   */
  async findOne(tenantId: string, id: string): Promise<OrderDetailResponseDto> {
    const order = await this.orderRepository.findById(id);

    if (!order) {
      throw new OrderNotFoundException(id);
    }

    if (order.tenantId !== tenantId) {
      throw new OrderTenantMismatchException(id, tenantId);
    }

    // Get order items
    const items = await this.orderItemRepository.findByOrderId(id);

    // Get order status history
    const statusHistory =
      await this.orderStatusHistoryRepository.findByOrderId(id);

    // Get order assignment history
    const assignmentHistory =
      await this.orderAssignmentHistoryRepository.findByOrderId(id);

    // Get order stop history
    const stopHistory = await this.orderStopHistoryRepository.findByOrderId(id);

    // Map order to response DTO
    const orderResponse = this.mapOrderToDetailResponseDto(order);

    // Add items to response if found
    if (items && items.length > 0) {
      orderResponse.items = items.map((item) => ({
        id: item.id,
        orderId: item.orderId,
        packageTemplateId: item.packageTemplateId,
        itemType: item.itemType,
        quantity: item.quantity,
        weight: item.weight,
        weightUnit: item.weightUnit,
        length: item.length,
        width: item.width,
        height: item.height,
        dimensionUnit: item.dimensionUnit,
        volume: item.volume,
        declaredValue: item.declaredValue,
        description: item.description,
        notes: item.notes,
        imageUrl: item.imageUrl,
        createdAt: item.createdAt,
        updatedAt: item.updatedAt,
      }));
    }

    // Add status history to response if found
    if (statusHistory && statusHistory.length > 0) {
      orderResponse.statusHistory = statusHistory.map((history) => ({
        id: history.id,
        orderId: history.orderId,
        previousStatus: history.previousStatus,
        newStatus: history.newStatus,
        reason: history.reason,
        comments: history.comments,
        changedBy: history.changedBy,
        changedByName: '', // Will be populated from user service if needed
        changedAt: history.changedAt,
      }));
    }

    // Add assignment history to response if found
    if (assignmentHistory && assignmentHistory.length > 0) {
      orderResponse.assignmentHistory = assignmentHistory.map((history) => ({
        id: history.id,
        orderId: history.orderId,
        previousDriverId: history.previousAssigneeId || '',
        previousDriverName: '', // Will be populated from user service if needed
        newDriverId: history.newAssigneeId || '',
        newDriverName: '', // Will be populated from user service if needed
        previousVehicleId: history.previousVehicleId || '',
        previousVehicleDescription: '', // Will be populated from vehicle service if needed
        newVehicleId: history.assignedVehicleId || '',
        newVehicleDescription: '', // Will be populated from vehicle service if needed
        reason: history.reason,
        assignedBy: history.assignedById,
        assignedByName: '', // Will be populated from user service if needed
        assignedAt: history.createdAt,
      }));
    }

    // Add stop history to response if found
    if (stopHistory && stopHistory.length > 0) {
      orderResponse.stopHistory = stopHistory.map((history) => ({
        id: history.id,
        orderId: history.orderId,
        stopType: history.stopType,
        scheduledTime: history.scheduledTime,
        actualTime: history.actualTime,
        locationData: history.locationData,
        notes: history.notes,
        updatedBy: history.createdBy,
        updatedByName: '', // Will be populated from user service if needed
        updatedAt: history.createdAt,
      }));
    }

    return orderResponse;
  }

  /**
   * Get order by tracking number with detailed information
   */
  async findByTrackingNumber(
    tenantId: string,
    trackingNumber: string,
  ): Promise<OrderDetailResponseDto> {
    const order =
      await this.orderRepository.findByTrackingNumber(trackingNumber);

    if (!order) {
      throw new OrderNotFoundException(
        `Order with tracking number: ${trackingNumber} not found`,
      );
    }

    if (order.tenantId !== tenantId) {
      throw new OrderTenantMismatchException(order.id, tenantId);
    }

    // Use the findOne method to get detailed order information
    return this.findOne(tenantId, order.id);
  }

  /**
   * Update an order
   */
  async update(
    tenantId: string,
    id: string,
    userId: string,
    updateOrderDto: UpdateOrderDto,
  ): Promise<OrderResponseDto> {
    // 1. Get the order and validate access
    const order = await this.orderRepository.findById(id);

    if (!order) {
      throw new OrderNotFoundException(id);
    }

    if (order.tenantId !== tenantId) {
      throw new OrderTenantMismatchException(id, tenantId);
    }

    // 2. Check if order is locked
    if (order.isLocked) {
      throw new OrderLockedException(id, order.lockedBy || '');
    }

    // 3. Handle status changes via status service if status is updated
    if (updateOrderDto.status && updateOrderDto.status !== order.status) {
      if (
        !this.orderStatusService.isValidTransition(
          order.status,
          updateOrderDto.status,
        )
      ) {
        throw new InvalidOrderStatusTransitionException(
          id,
          order.status,
          updateOrderDto.status,
        );
      }
    }

    // 4. Validate delivery time if both dates are provided
    if (
      updateOrderDto.scheduledCollectionTime &&
      updateOrderDto.scheduledDeliveryTime &&
      updateOrderDto.scheduledDeliveryTime <=
        updateOrderDto.scheduledCollectionTime
    ) {
      throw new OrderDeliveryTimeInvalidException();
    }

    // Also check if only delivery time is updated but conflicts with existing collection time
    if (
      updateOrderDto.scheduledDeliveryTime &&
      !updateOrderDto.scheduledCollectionTime &&
      order.scheduledCollectionTime &&
      updateOrderDto.scheduledDeliveryTime <= order.scheduledCollectionTime
    ) {
      throw new OrderDeliveryTimeInvalidException();
    }

    // Also check if only collection time is updated but conflicts with existing delivery time
    if (
      updateOrderDto.scheduledCollectionTime &&
      !updateOrderDto.scheduledDeliveryTime &&
      order.scheduledDeliveryTime &&
      order.scheduledDeliveryTime <= updateOrderDto.scheduledCollectionTime
    ) {
      throw new OrderDeliveryTimeInvalidException();
    }

    // 5. Start transaction
    const queryRunner = this.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 6. Update order with provided fields
      const updateData: Partial<Order> = {
        updatedBy: userId,
      };

      if (updateOrderDto.referenceNumber !== undefined)
        updateData.referenceNumber = updateOrderDto.referenceNumber;
      if (updateOrderDto.packageTemplateId !== undefined)
        updateData.packageTemplateId = updateOrderDto.packageTemplateId;
      if (updateOrderDto.collectionAddressId !== undefined)
        updateData.collectionAddressId = updateOrderDto.collectionAddressId;
      if (updateOrderDto.collectionContactName !== undefined)
        updateData.collectionContactName = updateOrderDto.collectionContactName;
      if (updateOrderDto.collectionInstructions !== undefined)
        updateData.collectionInstructions =
          updateOrderDto.collectionInstructions;
      if (updateOrderDto.collectionSignatureRequired !== undefined)
        updateData.collectionSignatureRequired =
          updateOrderDto.collectionSignatureRequired;
      if (updateOrderDto.scheduledCollectionTime !== undefined)
        updateData.scheduledCollectionTime =
          updateOrderDto.scheduledCollectionTime;
      if (updateOrderDto.actualCollectionTime !== undefined)
        updateData.actualCollectionTime = updateOrderDto.actualCollectionTime;
      if (updateOrderDto.collectionZoneId !== undefined)
        updateData.collectionZoneId = updateOrderDto.collectionZoneId;
      if (updateOrderDto.deliveryAddressId !== undefined)
        updateData.deliveryAddressId = updateOrderDto.deliveryAddressId;
      if (updateOrderDto.deliveryContactName !== undefined)
        updateData.deliveryContactName = updateOrderDto.deliveryContactName;
      if (updateOrderDto.deliveryInstructions !== undefined)
        updateData.deliveryInstructions = updateOrderDto.deliveryInstructions;
      if (updateOrderDto.deliverySignatureRequired !== undefined)
        updateData.deliverySignatureRequired =
          updateOrderDto.deliverySignatureRequired;
      if (updateOrderDto.scheduledDeliveryTime !== undefined)
        updateData.scheduledDeliveryTime = updateOrderDto.scheduledDeliveryTime;
      if (updateOrderDto.actualDeliveryTime !== undefined)
        updateData.actualDeliveryTime = updateOrderDto.actualDeliveryTime;
      if (updateOrderDto.deliveryZoneId !== undefined)
        updateData.deliveryZoneId = updateOrderDto.deliveryZoneId;
      if (updateOrderDto.vehicleTypeId !== undefined)
        updateData.vehicleTypeId = updateOrderDto.vehicleTypeId;
      if (updateOrderDto.assignedDriverId !== undefined)
        updateData.assignedDriverId = updateOrderDto.assignedDriverId;
      if (updateOrderDto.assignedVehicleId !== undefined)
        updateData.assignedVehicleId = updateOrderDto.assignedVehicleId;
      if (updateOrderDto.codAmount !== undefined)
        updateData.codAmount = updateOrderDto.codAmount;
      if (updateOrderDto.codCollected !== undefined)
        updateData.codCollected = updateOrderDto.codCollected;
      if (updateOrderDto.codCollectionDate !== undefined)
        updateData.codCollectionDate = updateOrderDto.codCollectionDate;
      if (updateOrderDto.priceSetId !== undefined)
        updateData.priceSetId = updateOrderDto.priceSetId;
      if (updateOrderDto.basePriceType !== undefined)
        updateData.basePriceType = updateOrderDto.basePriceType;
      if (updateOrderDto.basePrice !== undefined)
        updateData.basePrice = updateOrderDto.basePrice;
      if (updateOrderDto.optionsPrice !== undefined)
        updateData.optionsPrice = updateOrderDto.optionsPrice;
      if (updateOrderDto.miscAdjustment !== undefined)
        updateData.miscAdjustment = updateOrderDto.miscAdjustment;
      if (updateOrderDto.customerAdjustment !== undefined)
        updateData.customerAdjustment = updateOrderDto.customerAdjustment;
      if (updateOrderDto.description !== undefined)
        updateData.description = updateOrderDto.description;
      if (updateOrderDto.comments !== undefined)
        updateData.comments = updateOrderDto.comments;
      if (updateOrderDto.internalNotes !== undefined)
        updateData.internalNotes = updateOrderDto.internalNotes;
      if (updateOrderDto.customFields !== undefined)
        updateData.customFields = updateOrderDto.customFields;
      if (updateOrderDto.metadata !== undefined)
        updateData.metadata = updateOrderDto.metadata;

      // 7. Update the order
      const updatedOrder = await this.orderRepository.update(
        id,
        tenantId,
        updateData,
      );

      if (!updatedOrder) {
        throw new OrderUpdateFailedException(
          id,
          'Failed to update order details',
        );
      }

      // 8. Handle status change if provided
      if (updateOrderDto.status && updateOrderDto.status !== order.status) {
        const statusUpdated = await this.orderStatusService.updateOrderStatus(
          updatedOrder,
          updateOrderDto.status,
          userId,
          'Status updated via order update',
        );

        if (!statusUpdated) {
          throw new OrderUpdateFailedException(
            id,
            'Failed to update order status',
          );
        }
      }

      // 9. Update order items if provided
      if (updateOrderDto.items && updateOrderDto.items.length > 0) {
        await this.orderItemRepository.bulkUpdate(updateOrderDto.items);

        // Recalculate order totals
        const totals = await this.orderItemRepository.calculateOrderTotals(id);

        if (totals) {
          await this.orderRepository.update(id, tenantId, {
            totalItems: totals.totalItems,
            totalWeight: totals.totalWeight,
            totalVolume: totals.totalVolume,
            declaredValue: totals.totalDeclaredValue,
            updatedBy: userId,
          });
        }
      }

      // 10. Update stop history if collection or delivery times changed
      if (updateOrderDto.scheduledCollectionTime) {
        // Create new collection stop if time changed
        await this.orderStopHistoryRepository.create(
          updatedOrder.id,
          OrderStopType.Collection,
          updateOrderDto.scheduledCollectionTime,
          userId,
        );
      }

      if (updateOrderDto.scheduledDeliveryTime) {
        // Create new delivery stop if time changed
        await this.orderStopHistoryRepository.create(
          updatedOrder.id,
          OrderStopType.Delivery,
          updateOrderDto.scheduledDeliveryTime,
          userId,
        );
      }

      // 11. Commit transaction
      await queryRunner.commitTransaction();

      // 12. Emit order updated event with enhanced payload for notifications
      const orderItems = await this.orderItemRepository.findByOrderId(id);
      const orderDetails = await this.findOne(tenantId, id);

      this.eventEmitter.emit('order.updated', {
        order: {
          ...orderDetails,
          items: orderItems,
          collection: {
            address: 'Collection Address', // This would be populated from the address service
            contactName: updatedOrder.collectionContactName ?? 'Contact',
            contactPhone: '', // This would be populated from the address service
            date: updatedOrder.scheduledCollectionTime,
            timeWindow: '9am-12pm', // This would be populated from the real data
          },
          delivery: {
            address: 'Delivery Address', // This would be populated from the address service
            contactName: updatedOrder.deliveryContactName ?? 'Contact',
            contactPhone: '', // This would be populated from the address service
            date: updatedOrder.scheduledDeliveryTime,
            timeWindow: '2pm-5pm', // This would be populated from the real data
          },
        },
        tenantId: updatedOrder.tenantId,
      });

      // 13. Get updated order with items
      const resultOrder = await this.orderRepository.findById(id);
      const items = await this.orderItemRepository.findByOrderId(id);

      // 14. Map to response DTO
      if (!resultOrder) {
        throw new OrderNotFoundException(id);
      }
      const orderResponse = this.mapOrderToResponseDto(resultOrder);

      // Add items to response if found
      if (items && items.length > 0) {
        orderResponse.items = items.map((item) => ({
          id: item.id,
          orderId: item.orderId,
          packageTemplateId: item.packageTemplateId,
          itemType: item.itemType,
          quantity: item.quantity,
          weight: item.weight,
          weightUnit: item.weightUnit,
          length: item.length,
          width: item.width,
          height: item.height,
          dimensionUnit: item.dimensionUnit,
          volume: item.volume,
          declaredValue: item.declaredValue,
          description: item.description,
          notes: item.notes,
          imageUrl: item.imageUrl,
          createdAt: item.createdAt,
          updatedAt: item.updatedAt,
        }));
      }

      return orderResponse;
    } catch (error) {
      // Rollback transaction in case of error
      await queryRunner.rollbackTransaction();

      // Re-throw if it's one of our custom exceptions
      if (
        error instanceof OrderNotFoundException ||
        error instanceof OrderTenantMismatchException ||
        error instanceof OrderLockedException ||
        error instanceof InvalidOrderStatusTransitionException ||
        error instanceof OrderUpdateFailedException ||
        error instanceof OrderDeliveryTimeInvalidException
      ) {
        throw error;
      }

      throw new OrderUpdateFailedException(id, error.message);
    } finally {
      // Release query runner
      await queryRunner.release();
    }
  }

  /**
   * Remove an order (soft delete)
   */
  async remove(tenantId: string, id: string, userId: string): Promise<void> {
    // 1. Get the order and validate access
    const order = await this.orderRepository.findById(id);

    if (!order) {
      throw new OrderNotFoundException(id);
    }

    if (order.tenantId !== tenantId) {
      throw new OrderTenantMismatchException(id, tenantId);
    }

    // 2. Check if order is locked
    if (order.isLocked) {
      throw new OrderLockedException(id, order.lockedBy || '');
    }

    // 3. Perform soft delete
    const deleted = await this.orderRepository.softDelete(id, tenantId, userId);

    if (!deleted) {
      throw new OrderUpdateFailedException(id, 'Failed to delete order');
    }

    // 4. Emit order deleted event
    this.eventEmitter.emit('order.deleted', {
      orderId: id,
      tenantId,
      userId,
    });
  }

  /**
   * Restore a soft-deleted order
   */
  async restore(
    tenantId: string,
    id: string,
    userId: string,
  ): Promise<OrderResponseDto> {
    // 1. Restore the order
    const restored = await this.orderRepository.restore(id, tenantId, userId);

    if (!restored) {
      throw new OrderNotFoundException(id);
    }

    // 2. Get the restored order
    const order = await this.orderRepository.findById(id);

    if (!order || order.tenantId !== tenantId) {
      throw new OrderNotFoundException(id);
    }

    // 3. Map to response DTO
    const orderResponse = this.mapOrderToResponseDto(order);

    // 4. Emit order restored event
    this.eventEmitter.emit('order.restored', {
      orderId: id,
      tenantId,
      userId,
    });

    return orderResponse;
  }

  /**
   * Permanently delete an order
   */
  async hardDelete(tenantId: string, id: string): Promise<void> {
    // 1. Get the order and validate access
    const order = await this.orderRepository.findById(id);

    if (!order) {
      throw new OrderNotFoundException(id);
    }

    if (order.tenantId !== tenantId) {
      throw new OrderTenantMismatchException(id, tenantId);
    }

    // 2. Start transaction
    const queryRunner = this.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 3. Delete related records first
      await this.orderItemRepository.deleteByOrderId(id);
      await this.orderStatusHistoryRepository.deleteByOrderId(id);
      await this.orderAssignmentHistoryRepository.deleteByOrderId(id);
      await this.orderStopHistoryRepository.deleteByOrderId(id);

      // 4. Delete the order itself
      const deleted = await this.orderRepository.hardDelete(id, tenantId);

      if (!deleted) {
        throw new OrderUpdateFailedException(
          id,
          'Failed to permanently delete order',
        );
      }

      // 5. Commit transaction
      await queryRunner.commitTransaction();

      // 6. Emit order permanently deleted event
      this.eventEmitter.emit('order.permanently.deleted', {
        orderId: id,
        tenantId,
      });
    } catch (error) {
      // Rollback transaction in case of error
      await queryRunner.rollbackTransaction();

      if (
        error instanceof OrderNotFoundException ||
        error instanceof OrderTenantMismatchException ||
        error instanceof OrderUpdateFailedException
      ) {
        throw error;
      }

      throw new OrderUpdateFailedException(
        id,
        `Failed to permanently delete order: ${error.message}`,
      );
    } finally {
      // Release query runner
      await queryRunner.release();
    }
  }

  /**
   * Change the status of an order
   */
  async changeStatus(
    tenantId: string,
    id: string,
    userId: string,
    status: OrderStatus,
    reason?: string,
    comments?: string,
  ): Promise<OrderResponseDto> {
    // 1. Get the order and validate access
    const order = await this.orderRepository.findById(id);

    if (!order) {
      throw new OrderNotFoundException(id);
    }

    if (order.tenantId !== tenantId) {
      throw new OrderTenantMismatchException(id, tenantId);
    }

    // 2. Check if order is locked
    if (order.isLocked) {
      throw new OrderLockedException(id, order.lockedBy || '');
    }

    // Save the previous status for notification
    const previousStatus = order.status;

    // 3. Try to update status
    const updatedOrder = await this.orderStatusService.updateOrderStatus(
      order,
      status,
      userId,
      reason,
      comments,
    );

    if (!updatedOrder) {
      throw new InvalidOrderStatusTransitionException(
        order.id,
        order.status,
        status,
      );
    }

    // 4. Get updated order with fresh data
    const refreshedOrder = await this.orderRepository.findById(id);

    if (!refreshedOrder) {
      throw new OrderNotFoundException(order.id);
    }

    // 5. Emit status change event with enhanced payload for notifications
    const orderItems = await this.orderItemRepository.findByOrderId(id);
    const orderDetails = await this.findOne(tenantId, id);

    this.eventEmitter.emit('order.status.changed', {
      order: {
        ...orderDetails,
        items: orderItems,
        previousStatus,
        currentStatus: status,
        statusNotes: comments || reason,
        collection: {
          address: 'Collection Address', // This would be populated from the address service
          contactName: refreshedOrder.collectionContactName ?? 'Contact',
          contactPhone: '', // This would be populated from the address service
          date: refreshedOrder.scheduledCollectionTime,
          timeWindow: '9am-12pm', // This would be populated from the real data
        },
        delivery: {
          address: 'Delivery Address', // This would be populated from the address service
          contactName: refreshedOrder.deliveryContactName ?? 'Contact',
          contactPhone: '', // This would be populated from the address service
          date: refreshedOrder.scheduledDeliveryTime,
          timeWindow: '2pm-5pm', // This would be populated from the real data
        },
      },
      tenantId,
      previousStatus,
      currentStatus: status,
      notes: comments || reason,
    });

    // 6. Map to response DTO
    return this.mapOrderToResponseDto(refreshedOrder);
  }

  /**
   * Add an item to an order
   */
  async addOrderItem(
    tenantId: string,
    orderId: string,
    userId: string,
    createItemDto: CreateOrderItemDto,
  ): Promise<OrderItem> {
    // 1. Get the order and validate access
    const order = await this.orderRepository.findById(orderId);

    if (!order) {
      throw new OrderNotFoundException(orderId);
    }

    if (order.tenantId !== tenantId) {
      throw new OrderTenantMismatchException(orderId, tenantId);
    }

    // 2. Check if order is locked
    if (order.isLocked) {
      throw new OrderLockedException(orderId, order.lockedBy || '');
    }

    // 3. Create the new item
    const newItem = await this.orderItemRepository.create(
      orderId,
      createItemDto,
    );

    // 4. Update order totals
    const totals = await this.orderItemRepository.calculateOrderTotals(orderId);

    if (totals) {
      await this.orderRepository.update(orderId, tenantId, {
        totalItems: totals.totalItems,
        totalWeight: totals.totalWeight,
        totalVolume: totals.totalVolume,
        declaredValue: totals.totalDeclaredValue,
        updatedBy: userId,
      });
    }

    return newItem;
  }

  /**
   * Update an existing order item
   */
  async updateOrderItem(
    tenantId: string,
    orderId: string,
    userId: string,
    updateItemDto: UpdateOrderItemDto,
  ): Promise<OrderItem> {
    // 1. Get the order and validate access
    const order = await this.orderRepository.findById(orderId);

    if (!order) {
      throw new OrderNotFoundException(orderId);
    }

    if (order.tenantId !== tenantId) {
      throw new OrderTenantMismatchException(orderId, tenantId);
    }

    // 2. Check if order is locked
    if (order.isLocked) {
      throw new OrderLockedException(orderId, order.lockedBy || '');
    }

    // 3. Check if the item exists and belongs to this order
    const item = await this.orderItemRepository.findById(updateItemDto.id);

    if (!item) {
      throw new OrderItemNotFoundException(updateItemDto.id);
    }

    if (item.orderId !== orderId) {
      throw new InvalidOrderDataException(
        `Item with ID ${updateItemDto.id} does not belong to order ${orderId}`,
        'itemId',
      );
    }

    // 4. Update the item
    const updatedItem = await this.orderItemRepository.update(
      updateItemDto.id,
      updateItemDto,
    );

    if (!updatedItem) {
      throw new OrderUpdateFailedException(
        orderId,
        `Failed to update item ${updateItemDto.id}`,
      );
    }

    // 5. Update order totals
    const totals = await this.orderItemRepository.calculateOrderTotals(orderId);

    if (totals) {
      await this.orderRepository.update(orderId, tenantId, {
        totalItems: totals.totalItems,
        totalWeight: totals.totalWeight,
        totalVolume: totals.totalVolume,
        declaredValue: totals.totalDeclaredValue,
        updatedBy: userId,
      });
    }

    return updatedItem;
  }

  /**
   * Remove an item from an order
   */
  async removeOrderItem(
    tenantId: string,
    orderId: string,
    userId: string,
    itemId: string,
  ): Promise<void> {
    // 1. Get the order and validate access
    const order = await this.orderRepository.findById(orderId);

    if (!order) {
      throw new OrderNotFoundException(orderId);
    }

    if (order.tenantId !== tenantId) {
      throw new OrderTenantMismatchException(orderId, tenantId);
    }

    // 2. Check if order is locked
    if (order.isLocked) {
      throw new OrderLockedException(orderId, order.lockedBy || '');
    }

    // 3. Check if the item exists and belongs to this order
    const item = await this.orderItemRepository.findById(itemId);

    if (!item) {
      throw new OrderItemNotFoundException(itemId);
    }

    if (item.orderId !== orderId) {
      throw new InvalidOrderDataException(
        `Item with ID ${itemId} does not belong to order ${orderId}`,
        'itemId',
      );
    }

    // 4. Delete the item
    const deleted = await this.orderItemRepository.delete(itemId);

    if (!deleted) {
      throw new OrderUpdateFailedException(
        orderId,
        `Failed to delete item ${itemId}`,
      );
    }

    // 5. Update order totals
    const totals = await this.orderItemRepository.calculateOrderTotals(orderId);

    if (totals) {
      await this.orderRepository.update(orderId, tenantId, {
        totalItems: totals.totalItems,
        totalWeight: totals.totalWeight,
        totalVolume: totals.totalVolume,
        declaredValue: totals.totalDeclaredValue,
        updatedBy: userId,
      });
    }
  }

  /**
   * Lock an order to prevent concurrent modifications
   */
  async lockOrder(
    tenantId: string,
    id: string,
    userId: string,
    reason?: string,
  ): Promise<boolean> {
    // 1. Get the order and validate access
    const order = await this.orderRepository.findById(id);

    if (!order) {
      throw new OrderNotFoundException(id);
    }

    if (order.tenantId !== tenantId) {
      throw new OrderTenantMismatchException(id, tenantId);
    }

    // 2. Check if order is already locked
    if (order.isLocked) {
      throw new OrderLockedException(id, order.lockedBy || '');
    }

    // 3. Lock the order
    return this.orderRepository.lockOrder(id, userId, reason);
  }

  /**
   * Unlock an order
   */
  async unlockOrder(
    tenantId: string,
    id: string,
    userId: string,
  ): Promise<boolean> {
    // 1. Get the order and validate access
    const order = await this.orderRepository.findById(id);

    if (!order) {
      throw new OrderNotFoundException(id);
    }

    if (order.tenantId !== tenantId) {
      throw new OrderTenantMismatchException(id, tenantId);
    }

    // 2. Check if order is locked by someone else
    if (order.isLocked && order.lockedBy !== userId) {
      throw new OrderUpdateFailedException(
        id,
        `Order is locked by user ${order.lockedBy} and can only be unlocked by them or an admin`,
      );
    }

    // 3. Unlock the order
    return this.orderRepository.unlockOrder(id);
  }

  /**
   * Get order status history
   */
  async getOrderStatusHistory(tenantId: string, id: string): Promise<any[]> {
    // 1. Get the order and validate access
    const order = await this.orderRepository.findById(id);

    if (!order) {
      throw new OrderNotFoundException(id);
    }

    if (order.tenantId !== tenantId) {
      throw new OrderTenantMismatchException(id, tenantId);
    }

    // 2. Get status history
    return this.orderStatusHistoryRepository.findByOrderId(id);
  }

  /**
   * Get order assignment history
   */
  async getOrderAssignmentHistory(
    tenantId: string,
    id: string,
  ): Promise<any[]> {
    // 1. Get the order and validate access
    const order = await this.orderRepository.findById(id);

    if (!order) {
      throw new OrderNotFoundException(id);
    }

    if (order.tenantId !== tenantId) {
      throw new OrderTenantMismatchException(id, tenantId);
    }

    // 2. Get assignment history
    return this.orderAssignmentHistoryRepository.findByOrderId(id);
  }

  /**
   * Get order stop history
   */
  async getOrderStopHistory(tenantId: string, id: string): Promise<any[]> {
    // 1. Get the order and validate access
    const order = await this.orderRepository.findById(id);

    if (!order) {
      throw new OrderNotFoundException(id);
    }

    if (order.tenantId !== tenantId) {
      throw new OrderTenantMismatchException(id, tenantId);
    }

    // 2. Get stop history
    return this.orderStopHistoryRepository.findByOrderId(id);
  }

  /**
   * Create a draft order with minimal information
   * Only requires pickup and delivery locations
   */
  async createDraftOrder(
    tenantId: string,
    userId: string,
    draftOrderDto: DraftOrderDto,
  ): Promise<DraftOrderResponseDto> {
    const queryRunner = this.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Generate tracking number
      const trackingNumber =
        this.trackingNumberService.generateTrackingNumber();

      // Create draft order with minimal required fields
      const order = new Order();
      order.tenantId = tenantId;
      order.trackingNumber = trackingNumber;
      order.referenceNumber = draftOrderDto.referenceNumber;
      order.status = OrderStatus.Draft; // Always draft
      order.customerId = draftOrderDto.customerId || '';
      order.requestedById = userId; // User who created the draft
      order.submittedById = userId; // Same as requested by for drafts

      // Address information
      order.collectionAddressId = draftOrderDto.collectionAddressId;
      order.collectionContactName = draftOrderDto.collectionContactName;
      order.collectionInstructions = draftOrderDto.collectionInstructions;
      order.collectionSignatureRequired = false; // Default for draft

      order.deliveryAddressId = draftOrderDto.deliveryAddressId;
      order.deliveryContactName = draftOrderDto.deliveryContactName;
      order.deliveryInstructions = draftOrderDto.deliveryInstructions;
      order.deliverySignatureRequired = false; // Default for draft

      // Basic order info
      order.description = draftOrderDto.description;
      order.comments = draftOrderDto.comments;

      // Default values for draft orders
      order.totalItems = 0; // No items yet
      order.basePrice = 0; // No pricing yet
      order.optionsPrice = 0;
      order.miscAdjustment = 0;
      order.customerAdjustment = 0;
      order.billingStatus = BillingStatus.NotBilled;
      order.paymentStatus = PaymentStatus.Pending;
      order.distanceUnit = DistanceUnit.Kilometers;

      // System fields
      order.customFields = {};
      order.metadata = {
        isDraft: true,
        draftCreatedAt: new Date().toISOString(),
        completionRequired: [
          'package_details',
          'pickup_time',
          'delivery_time',
          'pricing',
        ],
      };
      order.isLocked = false;
      order.isDeleted = false;
      order.createdBy = userId;

      // Create the order
      const createdOrder = await this.orderRepository.create(order);

      // Create status history
      await this.orderStatusHistoryRepository.create(
        createdOrder.id,
        null,
        OrderStatus.Draft,
        userId,
        'Draft order created - awaiting completion',
        'Order created with basic pickup and delivery information',
      );

      await queryRunner.commitTransaction();

      // Emit draft order created event
      this.eventEmitter.emit('order.draft.created', {
        orderId: createdOrder.id,
        tenantId: createdOrder.tenantId,
        customerId: createdOrder.customerId,
        trackingNumber: createdOrder.trackingNumber,
        userId,
      });

      // Map to draft response
      return this.mapOrderToDraftResponseDto(createdOrder);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw new OrderCreationFailedException(
        `Failed to create draft order: ${error.message}`,
      );
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Map order domain object to draft response DTO
   */
  private mapOrderToDraftResponseDto(order: Order): DraftOrderResponseDto {
    const nextSteps: string[] = [];

    // Determine next steps based on what's missing
    if (order.totalItems === 0) {
      nextSteps.push('Add package details and items');
    }
    if (!order.scheduledCollectionTime) {
      nextSteps.push('Set pickup/collection time');
    }
    if (!order.scheduledDeliveryTime) {
      nextSteps.push('Set delivery time');
    }
    if (order.basePrice === 0) {
      nextSteps.push('Configure pricing and options');
    }
    if (!order.packageTemplateId) {
      nextSteps.push('Select package template');
    }

    return {
      id: order.id,
      tenantId: order.tenantId,
      trackingNumber: order.trackingNumber,
      referenceNumber: order.referenceNumber,
      status: order.status,
      customerId: order.customerId,
      customerName: undefined, // Will be populated with relations if needed
      collectionAddressId: order.collectionAddressId,
      collectionAddressSummary: undefined, // Will be populated with relations if needed
      collectionContactName: order.collectionContactName,
      collectionInstructions: order.collectionInstructions,
      deliveryAddressId: order.deliveryAddressId,
      deliveryAddressSummary: undefined, // Will be populated with relations if needed
      deliveryContactName: order.deliveryContactName,
      deliveryInstructions: order.deliveryInstructions,
      description: order.description,
      comments: order.comments,
      createdAt: order.createdAt,
      updatedAt: order.updatedAt,
      isDraft: true,
      nextSteps,
    };
  }

  // Add this method to the OrdersService class

  /**
   * Complete a draft order by adding all required information
   */
  async completeDraftOrder(
    tenantId: string,
    draftOrderId: string,
    userId: string,
    completeOrderDto: CreateOrderDto,
  ): Promise<OrderResponseDto> {
    // 1. Get the draft order and validate
    const draftOrder = await this.orderRepository.findById(draftOrderId);
    if (!draftOrder) {
      throw new OrderNotFoundException(draftOrderId);
    }

    if (draftOrder.tenantId !== tenantId) {
      throw new OrderTenantMismatchException(draftOrderId, tenantId);
    }

    if (draftOrder.status !== OrderStatus.Draft) {
      throw new InvalidOrderDataException(
        `Order ${draftOrderId} is not a draft order. Current status: ${draftOrder.status}`,
        'status',
      );
    }

    // 2. Validate delivery time if provided
    if (
      completeOrderDto.scheduledCollectionTime &&
      completeOrderDto.scheduledDeliveryTime &&
      completeOrderDto.scheduledDeliveryTime <=
        completeOrderDto.scheduledCollectionTime
    ) {
      throw new OrderDeliveryTimeInvalidException();
    }

    const queryRunner = this.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 3. Update the draft order with complete information
      const updateData: Partial<Order> = {
        // Use provided values or keep existing draft values
        referenceNumber:
          completeOrderDto.referenceNumber || draftOrder.referenceNumber,
        status: completeOrderDto.status || OrderStatus.Submitted, // Move from Draft to Submitted
        customerId: completeOrderDto.customerId || draftOrder.customerId,
        requestedById:
          completeOrderDto.requestedById || draftOrder.requestedById,
        submittedById: completeOrderDto.submittedById || userId,

        // Package and template info
        packageTemplateId: completeOrderDto.packageTemplateId,

        // Collection details (merge with existing)
        collectionAddressId:
          completeOrderDto.collectionAddressId ||
          draftOrder.collectionAddressId,
        collectionContactName:
          completeOrderDto.collectionContactName ||
          draftOrder.collectionContactName,
        collectionInstructions:
          completeOrderDto.collectionInstructions ||
          draftOrder.collectionInstructions,
        collectionSignatureRequired:
          completeOrderDto.collectionSignatureRequired ?? false,
        scheduledCollectionTime: completeOrderDto.scheduledCollectionTime,
        collectionZoneId: completeOrderDto.collectionZoneId,

        // Delivery details (merge with existing)
        deliveryAddressId:
          completeOrderDto.deliveryAddressId || draftOrder.deliveryAddressId,
        deliveryContactName:
          completeOrderDto.deliveryContactName ||
          draftOrder.deliveryContactName,
        deliveryInstructions:
          completeOrderDto.deliveryInstructions ||
          draftOrder.deliveryInstructions,
        deliverySignatureRequired:
          completeOrderDto.deliverySignatureRequired ?? false,
        scheduledDeliveryTime: completeOrderDto.scheduledDeliveryTime,
        deliveryZoneId: completeOrderDto.deliveryZoneId,

        // Package details
        totalItems: completeOrderDto.totalItems ?? 1,
        totalWeight: completeOrderDto.totalWeight,
        totalVolume: completeOrderDto.totalVolume,
        declaredValue: completeOrderDto.declaredValue,

        // Vehicle and assignment
        vehicleTypeId: completeOrderDto.vehicleTypeId,
        assignedDriverId: completeOrderDto.assignedDriverId,
        assignedVehicleId: completeOrderDto.assignedVehicleId,

        // COD information
        codAmount: completeOrderDto.codAmount,

        // Pricing
        priceSetId: completeOrderDto.priceSetId,
        basePriceType: completeOrderDto.basePriceType,

        // Distance and logistics
        distance: completeOrderDto.distance,
        distanceUnit: completeOrderDto.distanceUnit || DistanceUnit.Kilometers,

        // Descriptions
        description: completeOrderDto.description || draftOrder.description,
        comments: completeOrderDto.comments || draftOrder.comments,
        internalNotes: completeOrderDto.internalNotes,

        // Custom data
        customFields: completeOrderDto.customFields || {},
        metadata: {
          ...draftOrder.metadata,
          ...completeOrderDto.metadata,
          completedFromDraft: true,
          completedAt: new Date().toISOString(),
          completedBy: userId,
        },

        updatedBy: userId,
      };

      // 4. Update the order
      const updatedOrder = await this.orderRepository.update(
        draftOrderId,
        tenantId,
        updateData,
      );

      if (!updatedOrder) {
        throw new OrderUpdateFailedException(
          draftOrderId,
          'Failed to complete draft order',
        );
      }

      // 5. Add status history for the completion
      await this.orderStatusHistoryRepository.create(
        draftOrderId,
        OrderStatus.Draft,
        updatedOrder.status,
        userId,
        'Draft order completed',
        'Order moved from draft to active status with complete information',
      );

      // 6. Add order items if provided
      if (completeOrderDto.items && completeOrderDto.items.length > 0) {
        await this.orderItemRepository.createBulk(
          draftOrderId,
          completeOrderDto.items,
        );

        // Recalculate totals
        const totals =
          await this.orderItemRepository.calculateOrderTotals(draftOrderId);
        if (totals) {
          await this.orderRepository.update(draftOrderId, tenantId, {
            totalItems: totals.totalItems,
            totalWeight: totals.totalWeight,
            totalVolume: totals.totalVolume,
            declaredValue: totals.totalDeclaredValue,
          });
        }
      }

      // 7. Create stop history for scheduled times
      if (completeOrderDto.scheduledCollectionTime) {
        await this.orderStopHistoryRepository.create(
          draftOrderId,
          OrderStopType.Collection,
          completeOrderDto.scheduledCollectionTime,
          userId,
        );
      }

      if (completeOrderDto.scheduledDeliveryTime) {
        await this.orderStopHistoryRepository.create(
          draftOrderId,
          OrderStopType.Delivery,
          completeOrderDto.scheduledDeliveryTime,
          userId,
        );
      }

      await queryRunner.commitTransaction();

      // 8. Emit completion event
      this.eventEmitter.emit('order.draft.completed', {
        orderId: draftOrderId,
        tenantId: updatedOrder.tenantId,
        previousStatus: OrderStatus.Draft,
        currentStatus: updatedOrder.status,
        completedBy: userId,
      });

      // 9. Return the completed order
      const finalOrder = await this.orderRepository.findById(draftOrderId);
      if (!finalOrder) {
        throw new OrderNotFoundException(draftOrderId);
      }

      return this.mapOrderToResponseDto(finalOrder);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      if (
        error instanceof OrderNotFoundException ||
        error instanceof OrderTenantMismatchException ||
        error instanceof InvalidOrderDataException ||
        error instanceof OrderUpdateFailedException ||
        error instanceof OrderDeliveryTimeInvalidException
      ) {
        throw error;
      }
      throw new OrderUpdateFailedException(
        draftOrderId,
        `Failed to complete draft order: ${error.message}`,
      );
    } finally {
      await queryRunner.release();
    }
  }
  /**
   * Map Order domain model to OrderResponseDto
   * This is a helper method for mapping the order to the response DTO
   */
  private mapOrderToResponseDto(order: Order): OrderResponseDto {
    const dto = new OrderResponseDto();
    dto.id = order.id;
    dto.tenantId = order.tenantId;
    dto.trackingNumber = order.trackingNumber;
    dto.referenceNumber = order.referenceNumber;
    dto.status = order.status;
    dto.customerId = order.customerId;
    dto.requestedById = order.requestedById;
    dto.collectionAddressId = order.collectionAddressId;
    dto.collectionContactName = order.collectionContactName;
    dto.collectionInstructions = order.collectionInstructions;
    dto.collectionSignatureRequired = order.collectionSignatureRequired;
    dto.scheduledCollectionTime = order.scheduledCollectionTime;
    dto.actualCollectionTime = order.actualCollectionTime;
    dto.deliveryAddressId = order.deliveryAddressId;
    dto.deliveryContactName = order.deliveryContactName;
    dto.deliveryInstructions = order.deliveryInstructions;
    dto.deliverySignatureRequired = order.deliverySignatureRequired;
    dto.scheduledDeliveryTime = order.scheduledDeliveryTime;
    dto.actualDeliveryTime = order.actualDeliveryTime;
    dto.packageTemplateId = order.packageTemplateId;
    dto.totalItems = order.totalItems;
    dto.totalWeight = order.totalWeight;
    dto.totalVolume = order.totalVolume;
    dto.declaredValue = order.declaredValue;
    dto.assignedDriverId = order.assignedDriverId;
    dto.assignedVehicleId = order.assignedVehicleId;
    dto.basePrice = order.basePrice;
    dto.optionsPrice = order.optionsPrice;
    dto.miscAdjustment = order.miscAdjustment;
    dto.customerAdjustment = order.customerAdjustment;
    dto.totalPrice = order.totalPrice;
    dto.billingStatus = order.billingStatus;
    dto.paymentStatus = order.paymentStatus;
    dto.distance = order.distance;
    dto.distanceUnit = order.distanceUnit;
    dto.estimatedDuration = order.estimatedDuration;
    dto.description = order.description;
    dto.comments = order.comments;
    dto.internalNotes = order.internalNotes;
    dto.createdAt = order.createdAt;
    dto.updatedAt = order.updatedAt;

    return dto;
  }

  /**
   * Map Order domain object to OrderDetailResponseDto
   */
  private mapOrderToDetailResponseDto(order: Order): OrderDetailResponseDto {
    // First map the basic order properties
    const basicOrderResponse = this.mapOrderToResponseDto(order);

    // Create a new OrderDetailResponseDto and copy all properties from the basic response
    const detailResponseDto = new OrderDetailResponseDto();
    Object.assign(detailResponseDto, basicOrderResponse);

    // Initialize the history arrays
    detailResponseDto.statusHistory = [];
    detailResponseDto.assignmentHistory = [];
    detailResponseDto.stopHistory = [];

    return detailResponseDto;
  }
}
