import { AutoMap } from '@automapper/classes';

export class OrderItem {
  @AutoMap()
  id: string;

  @AutoMap()
  orderId: string;

  @AutoMap()
  packageTemplateId?: string;

  @AutoMap()
  itemType: string;

  @AutoMap()
  quantity: number;

  @AutoMap()
  weight?: number;

  @AutoMap()
  weightUnit?: string;

  @AutoMap()
  length?: number;

  @AutoMap()
  width?: number;

  @AutoMap()
  height?: number;

  @AutoMap()
  dimensionUnit?: string;

  @AutoMap()
  volume?: number;

  @AutoMap()
  declaredValue?: number;

  @AutoMap()
  description?: string;

  @AutoMap()
  notes?: string;

  @AutoMap()
  imageUrl?: string;

  @AutoMap()
  metadata?: Record<string, any>;

  @AutoMap()
  createdAt: Date;

  @AutoMap()
  updatedAt: Date;
}
