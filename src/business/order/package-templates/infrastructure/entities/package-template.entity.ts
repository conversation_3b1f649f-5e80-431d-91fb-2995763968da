import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { EntityRelationalHelper } from '@utils/relational-entity-helper';
import { PackageStatus } from '../../domain/package-template.types';
import { UserEntity } from '@app/business/user/users/infrastructure/entities/user.entity';

@Entity('package_templates')
export class PackageTemplateEntity extends EntityRelationalHelper {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'tenant_id' })
  @Index()
  tenantId: string;

  @Column({ length: 255 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: PackageStatus,
    default: PackageStatus.Active,
  })
  status: PackageStatus;

  @Column({ type: 'jsonb', default: '[]' })
  capabilities: string[];

  @Column({ name: 'dimensions_required', default: false })
  dimensionsRequired: boolean;

  @Column({ name: 'weight_required', default: false })
  weightRequired: boolean;

  @Column({
    name: 'max_weight',
    type: 'decimal',
    precision: 10,
    scale: 2,
    nullable: true,
  })
  maxWeight: number;

  @Column({
    name: 'max_volume',
    type: 'decimal',
    precision: 10,
    scale: 2,
    nullable: true,
  })
  maxVolume: number;

  @Column({ name: 'price_calculation_rules', type: 'jsonb', nullable: true })
  priceCalculationRules: Record<string, any>;

  @Column({ name: 'requires_signature', default: false })
  requiresSignature: boolean;

  @Column({ name: 'requires_insurance', default: false })
  requiresInsurance: boolean;

  @Column({
    name: 'special_handling_instructions',
    type: 'text',
    nullable: true,
  })
  specialHandlingInstructions: string;

  @Column({
    name: 'vehicle_type_restrictions',
    type: 'uuid',
    array: true,
    nullable: true,
  })
  vehicleTypeRestrictions: string[];

  @Column({
    name: 'available_zones',
    type: 'uuid',
    array: true,
    nullable: true,
  })
  availableZones: string[];

  @Column({ type: 'jsonb', default: '{}' })
  metadata: Record<string, any>;

  @Column({ name: 'is_deleted', default: false })
  isDeleted: boolean;

  @DeleteDateColumn({
    name: 'deleted_at',
    type: 'timestamp with time zone',
    nullable: true,
  })
  deletedAt: Date;

  @CreateDateColumn({ name: 'created_at', type: 'timestamp with time zone' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamp with time zone' })
  updatedAt: Date;

  @Column({ name: 'created_by' })
  createdBy: string;

  @ManyToOne(() => UserEntity)
  @JoinColumn({ name: 'created_by' })
  createdByUser: UserEntity;

  @Column({ name: 'updated_by', nullable: true })
  updatedBy: string;

  @ManyToOne(() => UserEntity)
  @JoinColumn({ name: 'updated_by' })
  updatedByUser: UserEntity;
}
