import { HttpStatus, Injectable } from '@nestjs/common';
import { PackageTemplateRepository } from './infrastructure/repositories/package-template.repository';
import { CreatePackageTemplateDto } from './dto/create-package-template.dto';
import { UpdatePackageTemplateDto } from './dto/update-package-template.dto';
import { PackageTemplate } from './domain/package-template';
import { AppException } from '@utils/errors/app.exception';
import { ErrorCode } from '@utils/errors/error-codes';
import { PackageTemplateMapper } from './infrastructure/mappers/package-template.mapper';
import { PackageTemplateResponseDto } from './dto/package-template-response.dto';
import { BaseFilterDto } from '@core/infrastructure/filtering/dtos/base-filter.dto';
import { PaginatedResult } from '@utils/query-creator/interfaces';
import { PackageStatus } from './domain/package-template.types';
import {
  PackageTemplateNotFoundException,
  PackageTemplateCreationFailedException,
  PackageTemplateUpdateFailedException,
  PackageTemplateOperationNotAllowedException,
  PackageTemplateRestoreFailedException,
  PackageTemplateTenantMismatchException,
} from '@utils/errors/exceptions/package-template.exceptions';

@Injectable()
export class PackageTemplatesService {
  constructor(
    private readonly packageTemplateRepository: PackageTemplateRepository,
  ) {}

  async create(
    tenantId: string,
    userId: string,
    createDto: CreatePackageTemplateDto,
  ): Promise<PackageTemplateResponseDto> {
    try {
      const packageTemplate = new PackageTemplate();

      // Mandatory fields
      packageTemplate.tenantId = tenantId;
      packageTemplate.name = createDto.name;
      packageTemplate.capabilities = createDto.capabilities;
      packageTemplate.createdBy = userId;

      // Optional fields with defaults
      packageTemplate.status = createDto.status || PackageStatus.Active;
      packageTemplate.dimensionsRequired =
        createDto.dimensionsRequired ?? false;
      packageTemplate.weightRequired = createDto.weightRequired ?? false;
      packageTemplate.requiresSignature = createDto.requiresSignature ?? false;
      packageTemplate.requiresInsurance = createDto.requiresInsurance ?? false;
      packageTemplate.isDeleted = false;

      // Optional fields
      if (createDto.description !== undefined)
        packageTemplate.description = createDto.description;
      if (createDto.maxWeight !== undefined)
        packageTemplate.maxWeight = createDto.maxWeight;
      if (createDto.maxVolume !== undefined)
        packageTemplate.maxVolume = createDto.maxVolume;
      if (createDto.priceCalculationRules !== undefined)
        packageTemplate.priceCalculationRules = createDto.priceCalculationRules;
      if (createDto.specialHandlingInstructions !== undefined)
        packageTemplate.specialHandlingInstructions =
          createDto.specialHandlingInstructions;
      if (createDto.vehicleTypeRestrictions !== undefined)
        packageTemplate.vehicleTypeRestrictions =
          createDto.vehicleTypeRestrictions;
      if (createDto.availableZones !== undefined)
        packageTemplate.availableZones = createDto.availableZones;
      if (createDto.metadata !== undefined)
        packageTemplate.metadata = createDto.metadata;

      const createdPackageTemplate =
        await this.packageTemplateRepository.create(packageTemplate);

      return PackageTemplateMapper.toResponseDto(createdPackageTemplate);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      throw new PackageTemplateCreationFailedException(error.message);
    }
  }

  async findAll(
    tenantId: string,
    filter: BaseFilterDto,
  ): Promise<PaginatedResult<PackageTemplateResponseDto>> {
    try {
      return await this.packageTemplateRepository.findByTenantId(
        tenantId,
        filter,
      );
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        `Failed to retrieve package templates: ${error.message}`,
        ErrorCode.DATABASE_ERROR,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async findOne(
    tenantId: string,
    id: string,
  ): Promise<PackageTemplateResponseDto> {
    const packageTemplate = await this.packageTemplateRepository.findById(id);

    if (!packageTemplate) {
      throw new PackageTemplateNotFoundException(id);
    }

    if (packageTemplate.tenantId !== tenantId) {
      throw new PackageTemplateTenantMismatchException(id, tenantId);
    }

    return PackageTemplateMapper.toResponseDto(packageTemplate);
  }

  async update(
    tenantId: string,
    id: string,
    userId: string,
    updateDto: UpdatePackageTemplateDto,
  ): Promise<PackageTemplateResponseDto> {
    // First check if the package template exists and belongs to the tenant
    await this.findOne(tenantId, id);

    try {
      const updates: Partial<PackageTemplate> = {
        ...updateDto,
        updatedBy: userId,
      };

      const updatedPackageTemplate =
        await this.packageTemplateRepository.update(id, tenantId, updates);

      if (!updatedPackageTemplate) {
        throw new PackageTemplateUpdateFailedException(
          id,
          'Repository update failed',
        );
      }

      return PackageTemplateMapper.toResponseDto(updatedPackageTemplate);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      throw new PackageTemplateUpdateFailedException(id, error.message);
    }
  }

  async softDelete(
    tenantId: string,
    id: string,
    userId: string,
  ): Promise<void> {
    // First check if the package template exists and belongs to the tenant
    await this.findOne(tenantId, id);

    const success = await this.packageTemplateRepository.softDelete(
      id,
      tenantId,
      userId,
    );

    if (!success) {
      throw new PackageTemplateOperationNotAllowedException(
        id,
        'soft delete',
        'The operation could not be completed',
      );
    }
  }

  async restore(
    tenantId: string,
    id: string,
    userId: string,
  ): Promise<PackageTemplateResponseDto> {
    // First check if the package template exists
    const packageTemplate = await this.packageTemplateRepository.findById(id);

    if (!packageTemplate) {
      throw new PackageTemplateNotFoundException(id);
    }

    if (packageTemplate.tenantId !== tenantId) {
      throw new PackageTemplateTenantMismatchException(id, tenantId);
    }

    const success = await this.packageTemplateRepository.restore(
      id,
      tenantId,
      userId,
    );

    if (!success) {
      throw new PackageTemplateRestoreFailedException(
        id,
        'Could not restore the template',
      );
    }

    // Return the restored template
    return this.findOne(tenantId, id);
  }

  async hardDelete(tenantId: string, id: string): Promise<void> {
    // First check if the package template exists and belongs to the tenant
    await this.findOne(tenantId, id);

    const success = await this.packageTemplateRepository.hardDelete(
      id,
      tenantId,
    );

    if (!success) {
      throw new PackageTemplateOperationNotAllowedException(
        id,
        'permanently delete',
        'The operation could not be completed',
      );
    }
  }
}
