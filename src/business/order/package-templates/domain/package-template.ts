import { PackageStatus } from './package-template.types';

export class PackageTemplate {
  id: string;
  tenantId: string;
  name: string;
  description?: string;
  status: PackageStatus;
  capabilities: string[]; // Array of supported package types
  dimensionsRequired: boolean;
  weightRequired: boolean;
  maxWeight?: number;
  maxVolume?: number;
  priceCalculationRules?: Record<string, any>; // JSONB - Rules for price calculation
  requiresSignature: boolean;
  requiresInsurance: boolean;
  specialHandlingInstructions?: string;
  vehicleTypeRestrictions?: string[]; // Array of UUIDs
  availableZones?: string[]; // Array of UUIDs
  metadata?: Record<string, any>; // JSONB
  isDeleted: boolean;
  deletedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  updatedBy?: string;
}
