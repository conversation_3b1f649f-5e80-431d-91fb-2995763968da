import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { FindOptionsWhere, In, Repository } from 'typeorm';

import { PriceSetEntity } from '../entities/price-set.entity';
import {
  getDayOfWeek,
  getTimeString,
  toTenantTimezone,
  compareDates,
  addTime,
  setTime,
  addDays,
  isWeekend,
} from '../../../utils/date.utils';
import { PriceSetDomain } from '../../domain/price-set';
import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { EntityCondition } from '@utils/types/entity-condition.type';
import { NullableType } from '@utils/types/nullable.type';
import { ScheduleEntity } from '../entities/schedule.entity';
import { CreateScheduleDto } from '../../dto/create-schedule.dto';
import {
  PriceSetNotFoundException,
  PriceSetOperationNotAllowedException,
} from '../../../../../utils/errors/exceptions/price-set-exceptions';
import { GetScheduleDto } from '../../dto/get-schedule.dto';
import {
  AvailabilityType,
  ConfigurationType,
  OffsetType,
} from '../../domain/price-set.types';
import { PriceSetAvailabilityDto } from '../../dto/get-price-set-by-date.dto';
import { PriceSetModifierEntity } from '../entities/price-set-modifier.entity';
import { PriceModifierEntity } from '../../../price-modifiers/infrastructure/entities/price-modifier.entity';
import {
  PriceModifierGroupNotFoundException,
  PriceModifierNotFoundException,
} from '../../../../../utils/errors/exceptions/price-modifier-exceptions';
import { PriceModifierDto } from '../../dto/get-price-modifiers.dto';
import { PriceSetCustomerEntity } from '../entities/price-set-customer.entity';
import { UserEntity } from '../../../../user/users/infrastructure/entities/user.entity';
import { UserNotFoundException } from '../../../../../utils/errors/exceptions/user.exceptions';
import { CustomerDto } from '../../dto/get-price-customers.dto';
import { BaseFilterDto } from '../../../../../core/infrastructure/filtering/dtos/base-filter.dto';
import { PaginatedResult } from '../../../../../utils/query-creator/interfaces';
import { SecureFilterService } from '../../../../../core/infrastructure/filtering/services/secure-filter.service';
import { PriceSetFilterConfig } from '../../price-set-filter.config';
import { AssignModifiersDto } from '../../dto/assign-modifiers.dto';
import { PriceModifierGroupEntity } from '../../../price-modifiers/infrastructure/entities/price-modifier-group.entity';
import { CreateZoneTableDto } from '../../../../zone/zone-tables/dto/create-zone-table.dto';
import { ZoneTableEntity } from '../../../../zone/zone-tables/infrastructure/entities/zone-table.entity';
import { ZoneTableValueEntity } from '../../../../zone/zone-tables/infrastructure/entities/zone-table-value.entity';
import { GetPriceSetZoneResponseDto } from '../../dto/get-price-zone.dto';
import { ZoneTableNotFoundException } from '../../../../../utils/errors/exceptions/zone-exceptions';

@Injectable()
export class PriceSetRepository {
  constructor(
    @InjectRepository(PriceSetEntity)
    private readonly priceSetRepository: Repository<PriceSetEntity>,
    @InjectRepository(ScheduleEntity)
    private readonly scheduleRepository: Repository<ScheduleEntity>,
    @InjectRepository(PriceSetModifierEntity)
    private readonly priceSetModifierRepository: Repository<PriceSetModifierEntity>,
    @InjectRepository(PriceModifierEntity)
    private readonly priceModifierRepository: Repository<PriceModifierEntity>,
    @InjectRepository(PriceModifierGroupEntity)
    private readonly priceModifierGroupRepository: Repository<PriceModifierGroupEntity>,
    @InjectRepository(PriceSetCustomerEntity)
    private readonly priceSetCustomerRepository: Repository<PriceSetCustomerEntity>,
    @InjectRepository(UserEntity)
    private readonly userRepository: Repository<UserEntity>,
    @InjectRepository(ZoneTableEntity)
    private readonly zoneTableRepository: Repository<ZoneTableEntity>,
    @InjectRepository(ZoneTableValueEntity)
    private readonly zoneTableValueRepository: Repository<ZoneTableValueEntity>,
    private readonly filterService: SecureFilterService,
    @InjectMapper() private readonly mapper: Mapper,
  ) {
    this.filterService = new SecureFilterService(PriceSetFilterConfig());
  }

  async create(data: PriceSetDomain): Promise<PriceSetDomain> {
    const requestEntity = this.mapper.map(data, PriceSetDomain, PriceSetEntity);
    const priceSetsEntity = await this.priceSetRepository.save(
      this.priceSetRepository.create(requestEntity),
    );
    const responseDomain = this.mapper.map(
      priceSetsEntity,
      PriceSetEntity,
      PriceSetDomain,
    );
    return responseDomain;
  }

  async duplicate(priceSetId: PriceSetDomain['id']): Promise<PriceSetDomain> {
    const priceSet = await this.priceSetRepository.findOne({
      where: { id: priceSetId },
    });
    if (!priceSet) {
      throw new PriceSetNotFoundException(priceSetId);
    }

    const duplicatePriceSet = await this.priceSetRepository.save(
      this.priceSetRepository.create({
        tenantId: priceSet.tenantId,
        name: `${priceSet.name} - Copy`,
        internalName: `${priceSet.internalName} - Copy`,
        paymentOption: priceSet.paymentOption,
        description: priceSet.description,
        notes: priceSet.notes,
        availabilityType: priceSet.availabilityType,
        offsetType: priceSet.offsetType,
        offsetData: priceSet.offsetData,
      }),
    );

    const schedules = await this.scheduleRepository.find({
      where: { priceSetId },
    });

    for (const schedule of schedules) {
      await this.scheduleRepository.save(
        this.scheduleRepository.create({
          days: schedule.days,
          startTime: schedule.startTime,
          endTime: schedule.endTime,
          priceSetId: duplicatePriceSet.id,
        }),
      );
    }

    const responseDomain = this.mapper.map(
      duplicatePriceSet,
      PriceSetEntity,
      PriceSetDomain,
    );
    return responseDomain;
  }

  async find(
    filter: BaseFilterDto,
    tenantId: string,
  ): Promise<PaginatedResult<PriceSetDomain>> {
    const queryBuilder = this.priceSetRepository
      .createQueryBuilder('priceSet')
      .where('priceSet.isDeleted = false')
      .andWhere('priceSet.tenantId = :tenantId', { tenantId });

    await this.filterService.buildQuery(queryBuilder, filter);
    const result = await this.filterService.executeQuery(queryBuilder, filter);

    const mappedData = this.mapper.mapArray(
      result.data,
      PriceSetEntity,
      PriceSetDomain,
    );

    return {
      ...result,
      data: mappedData,
    };
  }

  async findAll(tenantId: string): Promise<PriceSetDomain[]> {
    const priceSetEntities = await this.priceSetRepository.find({
      where: {
        tenantId,
        isDeleted: false,
      },
      select: ['id', 'name', 'internalName'],
      order: { createdAt: 'DESC' },
    });
    const responseDomain = this.mapper.mapArray(
      priceSetEntities,
      PriceSetEntity,
      PriceSetDomain,
    );
    return responseDomain;
  }

  async findOne(
    fields: EntityCondition<PriceSetDomain>,
  ): Promise<NullableType<PriceSetDomain>> {
    const requestEntity: Partial<PriceSetEntity> = this.mapper.map(
      fields,
      PriceSetDomain,
      PriceSetEntity,
    );
    const priceSetsEntity = await this.priceSetRepository.findOne({
      where: {
        ...(requestEntity as FindOptionsWhere<PriceSetEntity>),
        isDeleted: false,
      },
    });
    if (priceSetsEntity) {
      const responseDomain = this.mapper.map(
        priceSetsEntity,
        PriceSetEntity,
        PriceSetDomain,
      );
      return responseDomain;
    }
    return null;
  }

  async update(payload: PriceSetDomain): Promise<PriceSetDomain> {
    const requestEntity = this.mapper.map(
      payload,
      PriceSetDomain,
      PriceSetEntity,
    );
    const priceSetsEntity = await this.priceSetRepository.save(requestEntity);
    const responseDomain = this.mapper.map(
      priceSetsEntity,
      PriceSetEntity,
      PriceSetDomain,
    );
    return responseDomain;
  }

  async editSchedule(
    priceSetId: ScheduleEntity['priceSetId'],
    data: CreateScheduleDto,
  ): Promise<void> {
    const priceSetEntity = await this.priceSetRepository.findOne({
      where: { id: priceSetId },
    });

    if (!priceSetEntity) {
      throw new PriceSetNotFoundException(priceSetId);
    }

    priceSetEntity.availabilityType = data.availabilityType;
    priceSetEntity.offsetType = data.offsetType;
    priceSetEntity.offsetData = data.offsetData;

    await this.priceSetRepository.save(priceSetEntity);

    await this.scheduleRepository.delete({ priceSetId });
    if (data.schedule && data.schedule.length > 0) {
      for (const schedule of data.schedule) {
        await this.scheduleRepository.save(
          this.scheduleRepository.create({
            ...schedule,
            priceSetId,
          }),
        );
      }
    }
    return;
  }

  async findSchedule(
    priceSetId: ScheduleEntity['priceSetId'],
  ): Promise<NullableType<GetScheduleDto>> {
    const priceSetsEntity = await this.priceSetRepository.findOne({
      where: { id: priceSetId },
    });
    if (priceSetsEntity) {
      const scheduleEntity = await this.scheduleRepository.find({
        where: { priceSetId },
      });
      const responseDomain: GetScheduleDto = {
        priceSetId: priceSetsEntity.id,
        availabilityType: priceSetsEntity.availabilityType,
        offsetType: priceSetsEntity.offsetType,
        offsetData: priceSetsEntity.offsetData,
        schedule: scheduleEntity,
      };
      return responseDomain;
    }
    return null;
  }

  async findByPickupDate(
    pickupDate: Date,
    tenantId: string,
  ): Promise<Array<PriceSetAvailabilityDto>> {
    // Convert input date to tenant timezone to ensure consistent handling
    const tenantPickupDate = toTenantTimezone(pickupDate);
    console.log({ tenantId });

    const priceSets = await this.priceSetRepository.find({
      where: { isDeleted: false, isActive: true, tenantId },
    });

    const results: Array<PriceSetAvailabilityDto> = [];

    for (const priceSet of priceSets) {
      let isAvailable = false;
      let deliveryDate: Date | null = null;

      if (priceSet.availabilityType === AvailabilityType.Weekly) {
        const schedules = await this.scheduleRepository.find({
          where: { priceSetId: priceSet.id },
        });

        // Get day of week and time using our utility functions
        const pickupDay = getDayOfWeek(tenantPickupDate);
        const pickupTime = getTimeString(tenantPickupDate);

        for (const schedule of schedules) {
          const days = schedule.days.split(',');
          if (days.includes(pickupDay)) {
            if (
              pickupTime >= schedule.startTime &&
              pickupTime <= schedule.endTime
            ) {
              isAvailable = true;
              deliveryDate = this.calculateDeliveryDate(
                tenantPickupDate.toDate(),
                priceSet,
              );
              break;
            }
          }
        }
      } else if (priceSet.availabilityType === AvailabilityType.Always) {
        isAvailable = true;
        deliveryDate = this.calculateDeliveryDate(
          tenantPickupDate.toDate(),
          priceSet,
        );
      } else if (priceSet.availabilityType === AvailabilityType.Never) {
        isAvailable = false;
      }

      if (isAvailable && deliveryDate) {
        results.push({
          id: priceSet.id,
          name: priceSet.name,
          deliveryDate,
        });
      }
    }
    return results;
  }

  async assignPriceModifiers(
    priceSetId: PriceSetDomain['id'],
    assignModifiersDto: AssignModifiersDto,
  ): Promise<void> {
    const memberIds = assignModifiersDto.members.map((m) => m.id);

    const modifierIds = assignModifiersDto.members
      .filter((m) => !m.isGroup)
      .map((m) => m.id);

    const groupIds = assignModifiersDto.members
      .filter((m) => m.isGroup)
      .map((m) => m.id);

    const modifiers = await this.priceModifierRepository.findBy({
      id: In(modifierIds),
    });

    if (modifiers.length !== modifierIds.length) {
      const foundIds = modifiers.map((m) => m.id);
      const missingIds = modifierIds.filter((id) => !foundIds.includes(id));
      throw new PriceModifierNotFoundException(
        missingIds.length > 0
          ? missingIds[0]
          : 'One or more price modifiers not found',
      );
    }

    const groups = await this.priceModifierGroupRepository.findBy({
      id: In(groupIds),
    });

    if (groups.length !== groupIds.length) {
      const foundIds = groups.map((m) => m.id);
      const missingIds = groupIds.filter((id) => !foundIds.includes(id));
      throw new PriceModifierGroupNotFoundException(
        missingIds.length > 0
          ? missingIds[0]
          : 'One or more price modifier groups not found',
      );
    }

    const existingRelationships = await this.priceSetModifierRepository.find({
      where: { setId: priceSetId },
    });

    const existingMemberIds = existingRelationships.map((rel) => rel.memberId);

    const memberIdsToRemove = existingMemberIds.filter(
      (id) => !memberIds.includes(id),
    );

    const memberIdsToAdd = memberIds.filter(
      (id) => !existingMemberIds.includes(id),
    );

    if (memberIdsToRemove.length > 0) {
      await this.priceSetModifierRepository.delete({
        setId: priceSetId,
        memberId: In(memberIdsToRemove),
      });
    }

    if (memberIdsToAdd.length > 0) {
      const priceSetModifiers = memberIdsToAdd.map((memberId) => {
        const member = assignModifiersDto.members.find(
          (m) => m.id === memberId,
        );
        return this.priceSetModifierRepository.create({
          setId: priceSetId,
          memberId,
          isGroup: member?.isGroup,
        });
      });
      await this.priceSetModifierRepository.save(priceSetModifiers);
    }

    return;
  }

  async findModifiersByPriceSetId(
    priceSetId: string,
  ): Promise<PriceModifierDto[]> {
    const members = await this.priceSetModifierRepository.find({
      where: { setId: priceSetId },
    });
    const priceSetModifier: PriceModifierDto[] = [];

    for (const member of members) {
      if (member.isGroup) {
        const group = await this.priceModifierGroupRepository.findOne({
          where: { id: member.memberId },
        });

        if (!group) {
          throw new PriceModifierGroupNotFoundException(member.memberId);
        }

        priceSetModifier.push({
          id: member.id,
          name: group.name,
          memberId: member.memberId,
          isGroup: member.isGroup,
          configuration: member.configuration,
        });
      } else {
        const modifier = await this.priceModifierRepository.findOne({
          where: { id: member.memberId },
        });

        if (!modifier) {
          throw new PriceModifierNotFoundException(member.memberId);
        }

        priceSetModifier.push({
          id: member.id,
          name: modifier.name,
          memberId: member.memberId,
          isGroup: member.isGroup,
          configuration: member.configuration,
        });
      }
    }

    //Sort price set modifiers by name
    priceSetModifier.sort((a, b) => {
      if (a.name < b.name) {
        return -1;
      }
      if (a.name > b.name) {
        return 1;
      }
      return 0;
    });

    return priceSetModifier;
  }

  /**
   * Find all modifiers for a price set with complete data
   * Similar to findModifiersByPriceSetId but returns all fields from the entities
   * @param priceSetId The ID of the price set
   * @returns Array of complete modifier data including all fields
   */
  async findCompleteModifiersByPriceSetId(priceSetId: string): Promise<
    Array<{
      id: string;
      priceSetId: string;
      memberId: string;
      isGroup: boolean;
      configuration: ConfigurationType;
      group?: any;
      modifier?: any;
    }>
  > {
    const members = await this.priceSetModifierRepository.find({
      where: { setId: priceSetId },
    });

    const completeModifiers: Array<{
      id: string;
      priceSetId: string;
      memberId: string;
      isGroup: boolean;
      configuration: ConfigurationType;
      group?: any;
      modifier?: any;
    }> = [];

    for (const member of members) {
      if (member.isGroup) {
        const group = await this.priceModifierGroupRepository.findOne({
          where: { id: member.memberId },
        });

        if (!group) {
          throw new PriceModifierGroupNotFoundException(member.memberId);
        }

        // Get all members of the group
        const groupMembers = await this.priceModifierRepository
          .createQueryBuilder('pm')
          .innerJoin(
            'price_modifier_group_members',
            'pmgm',
            'pmgm.member_id = pm.id',
          )
          .where('pmgm.group_id = :groupId', { groupId: group.id })
          .getMany();
        completeModifiers.push({
          id: member.id,
          priceSetId: priceSetId,
          memberId: member.memberId,
          isGroup: true,
          configuration: member.configuration,
          // Group data
          group: {
            ...group,
            members: groupMembers.map((modifier) => ({
              id: modifier.id,
              name: modifier.name,
              calculationType: modifier.calculationType,
              fieldName: modifier.fieldName,
              applicableRangeMin: modifier.applicableRangeMin,
              applicableRangeMax: modifier.applicableRangeMax,
              calculationBase: modifier.calculationBase,
              increment: modifier.increment,
              amount: modifier.amount,
              tieredRanges: modifier.tieredRanges,
              tieredDefaultValue: modifier.tieredDefaultValue,
              isActive: modifier.isActive,
              metadata: modifier.metadata,
              createdAt: modifier.createdAt,
              updatedAt: modifier.updatedAt,
            })),
          },
        });
      } else {
        const modifier = await this.priceModifierRepository.findOne({
          where: { id: member.memberId },
        });

        if (!modifier) {
          throw new PriceModifierNotFoundException(member.memberId);
        }

        completeModifiers.push({
          id: member.id,
          priceSetId: priceSetId,
          memberId: member.memberId,
          isGroup: false,
          configuration: member.configuration,
          // Modifier data
          modifier: {
            id: modifier.id,
            tenantId: modifier.tenantId,
            name: modifier.name,
            calculationType: modifier.calculationType,
            fieldName: modifier.fieldName,
            applicableRangeMin: modifier.applicableRangeMin,
            applicableRangeMax: modifier.applicableRangeMax,
            calculationBase: modifier.calculationBase,
            increment: modifier.increment,
            amount: modifier.amount,
            tieredRanges: modifier.tieredRanges,
            tieredDefaultValue: modifier.tieredDefaultValue,
            isActive: modifier.isActive,
            metadata: modifier.metadata,
            createdAt: modifier.createdAt,
            updatedAt: modifier.updatedAt,
          },
        });
      }
    }

    // Sort by name
    completeModifiers.sort((a, b) => {
      const nameA = a.isGroup ? a.group.name : a.modifier.name;
      const nameB = b.isGroup ? b.group.name : b.modifier.name;

      if (nameA < nameB) {
        return -1;
      }
      if (nameA > nameB) {
        return 1;
      }
      return 0;
    });

    return completeModifiers;
  }

  async editConfiguration(
    setModifierId: string,
    configuration: ConfigurationType,
  ): Promise<void> {
    const priceSetModifier = await this.priceSetModifierRepository.findOne({
      where: { id: setModifierId },
    });
    if (!priceSetModifier) {
      throw new PriceSetOperationNotAllowedException(
        setModifierId,
        'editConfiguration',
        'Price Set Modifier not found',
      );
    }

    priceSetModifier.configuration = configuration;
    await this.priceSetModifierRepository.save(priceSetModifier);
    return;
  }

  async assignCustomers(
    priceSetId: PriceSetDomain['id'],
    customerIds: string[],
  ): Promise<void> {
    const customers = await this.userRepository.find({
      where: { id: In(customerIds) },
    });

    const foundCustomerIds = new Set(customers.map((c) => c.id));
    const missingCustomerIds = customerIds.filter(
      (id) => !foundCustomerIds.has(id),
    );

    if (missingCustomerIds.length > 0) {
      throw new UserNotFoundException(
        missingCustomerIds.length === 1
          ? missingCustomerIds[0]
          : missingCustomerIds,
      );
    }

    await this.priceSetCustomerRepository.delete({ setId: priceSetId });

    const priceSetCustomers = customerIds.map((customerId) =>
      this.priceSetCustomerRepository.create({ setId: priceSetId, customerId }),
    );

    await this.priceSetCustomerRepository.save(priceSetCustomers);
  }

  async findCustomersByPriceSetId(priceSetId: string): Promise<CustomerDto[]> {
    const customers = this.priceSetCustomerRepository
      .createQueryBuilder('psc')
      .select([
        'psc.id as id',
        'psc.customerId as "customerId"',
        'u.company_name as "companyName"',
        'u.contact_name as "contactName"',
      ])
      .innerJoin(UserEntity, 'u', 'u.id = psc.customerId')
      .where('psc.setId = :priceSetId', { priceSetId })
      .getRawMany();

    return customers;
  }

  async basePriceByZone(
    tenantId: string,
    priceSetId: string,
    data: CreateZoneTableDto,
  ): Promise<void> {
    const zoneTable = await this.zoneTableRepository.findOne({
      where: { priceSetId },
    });

    if (zoneTable) {
      await this.zoneTableValueRepository.delete({
        zoneTableId: zoneTable.id,
      });

      await this.zoneTableRepository.delete({ priceSetId });
    }

    const zoneTableEntity = await this.zoneTableRepository.save(
      this.zoneTableRepository.create({ ...data, tenantId, priceSetId }),
    );

    if (data.zoneTableValues?.length) {
      const zoneTableValues = data.zoneTableValues.map((value) => ({
        ...value,
        zoneTableId: zoneTableEntity.id,
      }));
      const zoneTableValueEntities =
        this.zoneTableValueRepository.create(zoneTableValues);
      await this.zoneTableValueRepository.save(zoneTableValueEntities);
    }

    return;
  }

  async getBasePriceByZone(
    priceSetId: string,
  ): Promise<GetPriceSetZoneResponseDto> {
    const zoneTableEntity = await this.zoneTableRepository.findOne({
      where: { priceSetId },
    });

    if (!zoneTableEntity) {
      throw new ZoneTableNotFoundException(priceSetId);
    }

    const zoneTableValueEntity = await this.zoneTableValueRepository.find({
      where: { zoneTableId: zoneTableEntity.id },
    });

    const data: GetPriceSetZoneResponseDto = {
      id: zoneTableEntity.id,
      name: zoneTableEntity.name,
      zoneTableValues: zoneTableValueEntity.map((value) => ({
        id: value.id,
        originZoneId: value.originZoneId,
        destinationZoneId: value.destinationZoneId,
        value: value.value,
      })),
    };

    return data;
  }

  private calculateDeliveryDate(
    pickupDate: Date,
    priceSet: PriceSetEntity,
  ): Date {
    // Convert to tenant timezone for consistent handling
    let deliveryDate = toTenantTimezone(pickupDate);

    if (priceSet.offsetType === OffsetType.To) {
      // Add hours and minutes using our utility function
      const hoursToAdd = priceSet.offsetData?.hours || 0;
      const minutesToAdd = priceSet.offsetData?.minutes || 0;

      deliveryDate = addTime(deliveryDate, hoursToAdd, minutesToAdd);
    } else if (
      priceSet.offsetType === OffsetType.By &&
      priceSet.offsetData?.time
    ) {
      // Set specific time using our utility function
      const [hours, minutes] = priceSet.offsetData.time.split(':').map(Number);
      deliveryDate = setTime(deliveryDate, hours, minutes, 0);

      // ToDo: If the pickup time is very close to delivery time, we should not allow the delivery

      // If the delivery time is earlier than pickup time, add a day
      if (compareDates(pickupDate, deliveryDate) >= 0) {
        deliveryDate = addDays(deliveryDate, 1);
      }

      // Add additional days if specified
      if (priceSet.offsetData.daysOut) {
        deliveryDate = addDays(deliveryDate, priceSet.offsetData.daysOut);
      }

      // Skip weekends if needed
      if (!priceSet.offsetData.includeWeekends) {
        while (isWeekend(deliveryDate)) {
          deliveryDate = addDays(deliveryDate, 1);
        }
      }
    }

    // Return as JavaScript Date object
    return deliveryDate.toDate();
  }
}
