import {
  Body,
  Controller,
  HttpCode,
  HttpStatus,
  Post,
  Req,
  UseGuards,
} from '@nestjs/common';
import {
  ApiCookieAuth,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import { PricingService } from './pricing.service';
import { JwtContactAuthGuard } from '../../../core/auth/guards/jwt-contact-auth.guard';

import {
  GetAvailableServicesRequestDto,
  GetAvailableServicesResponseDto,
} from './dto/get-available-services.dto';
import { CurrentUser } from '../../../core/auth/decorators/current-user.decorator';
import { JwtPayload } from '../../../core/auth/domain/auth.types';
import { RequestWithUser } from '../../../core/auth/interceptors/tenant-context.interceptor';
import { TenantValidationService } from '../../user/tenants/tenant-validation.service';

@ApiTags('Customer Portal - Pricing')
@Controller({
  path: 'customer-portal/pricing',
  version: '1',
})
@ApiCookieAuth()
@UseGuards(JwtContactAuthGuard)
export class PricingController {
  constructor(
    private readonly pricingService: PricingService,
    private readonly tenantValidationService: TenantValidationService,
  ) {}

  @Post('available-services')
  @ApiOperation({
    summary:
      'Get available services based on pickup date with optional pricing',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({ description: 'OK', type: GetAvailableServicesResponseDto })
  async getAvailableServices(
    @CurrentUser() contactData: JwtPayload,
    @Req() request: RequestWithUser,
    @Body() params: GetAvailableServicesRequestDto,
  ): Promise<GetAvailableServicesResponseDto> {
    try {
      const { id: tenantId } =
        await this.tenantValidationService.validateTenantAccess(request);
      const customerId = contactData.sub;
      if (!customerId) {
        return { data: [] };
      }
      const data = await this.pricingService.getAvailableServices(
        params,
        tenantId,
      );

      return { data };
    } catch (error) {
      // Handle specific errors if needed
      throw error;
    }
  }
}
