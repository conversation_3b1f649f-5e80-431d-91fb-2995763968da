import {
  Body,
  Controller,
  HttpCode,
  HttpStatus,
  Post,
  UseGuards,
} from '@nestjs/common';
import {
  ApiCookieAuth,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import { PricingService } from './pricing.service';
import { JwtContactAuthGuard } from '../../../core/auth/guards/jwt-contact-auth.guard';

import {
  GetAvailableServicesRequestDto,
  GetAvailableServicesResponseDto,
} from './dto/get-available-services.dto';
import { CurrentUser } from '../../../core/auth/decorators/current-user.decorator';
import { JwtPayload } from '../../../core/auth/domain/auth.types';
import { ContactsService } from '../../user/contacts/contacts.service';

@ApiTags('Customer Portal - Pricing')
@Controller({
  path: 'customer-portal/pricing',
  version: '1',
})
@ApiCookieAuth()
@UseGuards(JwtContactAuthGuard)
export class PricingController {
  constructor(
    private readonly contactsService: ContactsService,
    private readonly pricingService: PricingService,
  ) {}

  @Post('available-services')
  @ApiOperation({
    summary:
      'Get available services based on pickup date with optional pricing',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({ description: 'OK', type: GetAvailableServicesResponseDto })
  async getAvailableServices(
    @CurrentUser() contactData: JwtPayload,
    @Body() params: GetAvailableServicesRequestDto,
  ): Promise<GetAvailableServicesResponseDto> {
    try {
      const contact = await this.contactsService.findById(contactData.sub);

      const customerId = contactData.sub;
      if (!customerId) {
        return { data: [] };
      }
      if (!contact.tenantId) {
        throw new Error('Contact does not have a tenant ID');
      }

      const data = await this.pricingService.getAvailableServices(
        params,
        contact.tenantId,
      );

      return { data };
    } catch (error) {
      // Handle specific errors if needed
      throw error;
    }
  }
}
