import { Module } from '@nestjs/common';
import { PricingController } from './pricing.controller';
import { PricingService } from './pricing.service';
import { PriceSetsService } from '../../../business/pricing/price-sets/price-sets.service';
import { PriceCalculatorModule } from '../../../core/pricing/price-calculator.module';
import { RelationalPriceSetPersistenceModule } from '../../../business/pricing/price-sets/infrastructure/relational-persistence.module';
import { ZoneTableModule } from '../../../business/zone/zone-tables/zone-table.module';
import { AddressModule } from '../address/address.module';
import { RelationalZonePersistenceModule } from '../../../business/zone/zones/infrastructure/relational-persistence.module';

@Module({
  imports: [
    // Import modules that provide repositories and services
    RelationalPriceSetPersistenceModule,
    PriceCalculatorModule,
    ZoneTableModule,
    AddressModule,
    RelationalZonePersistenceModule,
  ],
  controllers: [PricingController],
  providers: [PricingService, PriceSetsService],
  exports: [PricingService],
})
export class PricingModule {}
