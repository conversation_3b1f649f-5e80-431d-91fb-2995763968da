import { Injectable } from '@nestjs/common';
import { PackageTemplatesService } from '../../order/package-templates/package-templates.service';
import { CustomerPackageResponseDto } from './dto/customer-package-response.dto';
import { PackageStatus } from '../../order/package-templates/domain/package-template.types';
import { ContactsService } from '@app/business/user/contacts/contacts.service';

@Injectable()
export class CustomerPackagesService {
  constructor(
    private readonly packageTemplatesService: PackageTemplatesService,
    private readonly contactsService: ContactsService,
  ) {}

  /**
   * Get all active package templates for a customer
   * @param contactId The ID of the authenticated contact
   */
  async getAllPackagesForCustomer(
    contactId: string,
  ): Promise<CustomerPackageResponseDto[]> {
    // Get the contact to find the tenant ID
    const contact = await this.contactsService.findById(contactId);

    if (!contact.tenantId) {
      return [];
    }

    // Get all packages for the tenant
    return this.getAllPackagesForTenant(contact.tenantId);
  }

  /**
   * Get all active package templates for a tenant
   */
  async getAllPackagesForTenant(
    tenantId: string,
  ): Promise<CustomerPackageResponseDto[]> {
    // Get all package templates for the tenant
    const filter = {
      pageNumber: 1,
      pageSize: 1000, // Set a high limit to get all packages
      sortField: 'createdAt',
      sortDirection: 'DESC',
      searchTerm: '',
      where: {
        status: {
          eq: PackageStatus.Active, // Only get active packages
        },
      },
    };

    const packages = await this.packageTemplatesService.findAll(
      tenantId,
      filter,
    );
    // Map to the simplified response format
    return packages.data.map((pkg) => this.mapToCustomerResponse(pkg));
  }

  /**
   * Map a package template to the customer response format
   */
  private mapToCustomerResponse(template: any): CustomerPackageResponseDto {
    return {
      id: template.id,
      name: template.name,
      description: template.description,
      status: template.status,
      capabilities: template.capabilities,
      dimensionsRequired: template.dimensionsRequired,
      weightRequired: template.weightRequired,
      maxWeight: template.maxWeight,
      maxVolume: template.maxVolume,
      requiresSignature: template.requiresSignature,
      requiresInsurance: template.requiresInsurance,
      specialHandlingInstructions: template.specialHandlingInstructions,
    };
  }
}
