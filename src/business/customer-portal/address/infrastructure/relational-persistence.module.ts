import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AddressEntity } from '../../../address/addresses/infrastructure/entities/address.entity';
import { UserEntity } from '../../../user/users/infrastructure/entities/user.entity';
import { AddressRepository } from './repositories/address.repository';
import { SecureFilterService } from '@core/infrastructure/filtering/services/secure-filter.service';
import { AddressFilterConfig } from '../address-filter.config';

@Module({
  imports: [TypeOrmModule.forFeature([AddressEntity, UserEntity])],
  providers: [
    AddressRepository,
    {
      provide: SecureFilterService,
      useFactory: () => new SecureFilterService(AddressFilterConfig()),
    },
  ],
  exports: [AddressRepository],
})
export class RelationalAddressPersistenceModule {}
