import { Injectable } from '@nestjs/common';
import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { AddressRepository } from './infrastructure/repositories/address.repository';
import { AddressDomain } from './domain/address';
import { AddressEntity } from '../../address/addresses/infrastructure/entities/address.entity';
import {
  AddressNotFoundException,
  AddressOperationNotAllowedException,
} from '@utils/errors/exceptions/address-exceptions';
import { BaseFilterDto } from '@core/infrastructure/filtering/dtos/base-filter.dto';
import { PaginatedResult } from '@utils/query-creator/interfaces';

@Injectable()
export class AddressService {
  constructor(
    private readonly addressRepository: AddressRepository,
    @InjectMapper() private readonly mapper: Mapper,
  ) {}

  async create(addressDomain: AddressDomain): Promise<AddressDomain> {
    // Check if this is set as default and update other addresses accordingly
    if (addressDomain.isDefaultForPickup) {
      await this.clearDefaultForPickup(addressDomain.customerId);
    }

    if (addressDomain.isDefaultForDelivery) {
      await this.clearDefaultForDelivery(addressDomain.customerId);
    }

    return await this.addressRepository.create(addressDomain);
  }

  async duplicateAddress(
    addressId: AddressDomain['id'],
  ): Promise<AddressDomain> {
    const addressDomain = await this.addressRepository.findOne({
      id: addressId,
    });
    if (!addressDomain) {
      throw new AddressNotFoundException(addressId);
    }

    const duplicateAddress = this.mapper.map(
      addressDomain,
      AddressDomain,
      AddressDomain,
    );

    // Create a new address without the ID
    const newAddress = { ...duplicateAddress };
    // @ts-expect-error - We need to remove the ID to create a new record
    newAddress.id = undefined;
    newAddress.isDefaultForPickup = false;
    newAddress.isDefaultForDelivery = false;

    return await this.addressRepository.create(newAddress);
  }

  async getAddressList(
    filter: BaseFilterDto,
    customerId: string,
  ): Promise<PaginatedResult<AddressDomain>> {
    return this.addressRepository.find(filter, customerId);
  }

  async getAddressDetails(addressId: string): Promise<AddressDomain> {
    const address = await this.addressRepository.findOne({ id: addressId });
    if (!address) {
      throw new AddressNotFoundException(addressId);
    }
    return address;
  }

  async updateAddressDetails(address: AddressDomain): Promise<void> {
    const existingAddress = await this.addressRepository.findOne({
      id: address.id,
    });
    if (!existingAddress) {
      throw new AddressNotFoundException(address.id);
    }

    // Check if this is set as default and update other addresses accordingly
    if (address.isDefaultForPickup) {
      await this.clearDefaultForPickup(address.customerId);
    }

    if (address.isDefaultForDelivery) {
      await this.clearDefaultForDelivery(address.customerId);
    }

    await this.addressRepository.update(address);
  }

  async softDeleteAddress(addressId: string): Promise<void> {
    const address = await this.addressRepository.findOne({ id: addressId });
    if (!address) {
      throw new AddressNotFoundException(addressId);
    }

    address.isDeleted = true;
    address.deletedAt = new Date();
    await this.addressRepository.update(address);
  }

  private async clearDefaultForPickup(customerId: string): Promise<void> {
    const defaultAddress =
      await this.addressRepository.findDefaultForPickup(customerId);
    if (defaultAddress) {
      defaultAddress.isDefaultForPickup = false;
      await this.addressRepository.update(defaultAddress);
    }
  }

  private async clearDefaultForDelivery(customerId: string): Promise<void> {
    const defaultAddress =
      await this.addressRepository.findDefaultForDelivery(customerId);
    if (defaultAddress) {
      defaultAddress.isDefaultForDelivery = false;
      await this.addressRepository.update(defaultAddress);
    }
  }

  /**
   * Get all addresses for a customer without pagination
   * @param customerId The customer ID
   * @returns Array of address domains
   */
  async getAllAddresses(customerId: string): Promise<AddressDomain[]> {
    const queryBuilder = this.addressRepository
      .createQueryBuilder('address')
      .where('address.isDeleted = false')
      .andWhere('address.customerId = :customerId', { customerId })
      .leftJoinAndSelect('address.customer', 'customer')
      .orderBy('address.createdAt', 'DESC');

    const addresses = await queryBuilder.getMany();

    return this.mapper.mapArray(addresses, AddressEntity, AddressDomain);
  }

  /**
   * Update address preferences (favorite/default status)
   * @param addressId The address ID
   * @param customerId The customer ID
   * @param preferences The preferences to update
   * @returns The updated address
   */
  async updateAddressPreferences(
    addressId: string,
    customerId: string,
    preferences: {
      isFavoriteForPickup?: boolean;
      isFavoriteForDelivery?: boolean;
      isDefaultForPickup?: boolean;
    },
  ): Promise<AddressDomain> {
    const address = await this.addressRepository.findOne({ id: addressId });
    if (!address) {
      throw new AddressNotFoundException(addressId);
    }

    if (address.customerId !== customerId) {
      throw new AddressOperationNotAllowedException(
        customerId,
        'update preferences',
        'Address does not belong to your customer account',
      );
    }

    // Update favorite status
    if (preferences.isFavoriteForPickup !== undefined) {
      address.isFavoriteForPickup = preferences.isFavoriteForPickup;
    }

    if (preferences.isFavoriteForDelivery !== undefined) {
      address.isFavoriteForDelivery = preferences.isFavoriteForDelivery;
    }

    // Update default status - only for pickup as per requirements
    if (preferences.isDefaultForPickup !== undefined) {
      if (preferences.isDefaultForPickup) {
        await this.clearDefaultForPickup(customerId);
      }
      address.isDefaultForPickup = preferences.isDefaultForPickup;
    }

    return await this.addressRepository.update(address);
  }
}
