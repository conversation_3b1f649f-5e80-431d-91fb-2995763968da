import { Injectable } from '@nestjs/common';
import { VehicleDomain } from './domain/vehicle';
import { VehicleRepository } from './infrastructure/repositories/vehicle.repository';
import { VehicleTypeRepository } from '../vehicle-types/infrastructure/repositories/vehicle-type.repository';
import {
  VehicleNotFoundException,
  VehicleTypeNotFoundException,
} from '@app/utils/errors/exceptions/vehicle-exceptions';
import { BaseFilterDto } from '@core/infrastructure/filtering/dtos/base-filter.dto';
import { PaginatedResult } from '../../../utils/query-creator/interfaces';

@Injectable()
export class VehiclesService {
  constructor(
    private readonly vehicleRepository: VehicleRepository,
    private readonly vehicleTypeRepository: VehicleTypeRepository,
  ) {}

  async create(vehicleDomain: VehicleDomain): Promise<VehicleDomain> {
    const vehicleType = await this.vehicleTypeRepository.findOne({
      id: vehicleDomain.vehicleTypeId,
    });
    if (!vehicleType) {
      throw new VehicleTypeNotFoundException(vehicleDomain.vehicleTypeId);
    }

    const vehicle = await this.vehicleRepository.create(vehicleDomain);
    return vehicle;
  }

  async getVehicleList(
    filter: BaseFilterDto,
    tenantId: string,
  ): Promise<PaginatedResult<VehicleDomain>> {
    const vehicleDomain = await this.vehicleRepository.find(filter, tenantId);
    return vehicleDomain;
  }

  async getVehicleDetails(
    vehicleId: VehicleDomain['id'],
  ): Promise<VehicleDomain> {
    const vehicleDomain = await this.vehicleRepository.findOne({
      id: vehicleId,
    });
    if (!vehicleDomain) {
      throw new VehicleNotFoundException(vehicleId);
    }
    return vehicleDomain;
  }

  async generateFleetId(): Promise<string> {
    const prefix = 'FLT';
    let isUnique = false;
    let fleetId = '';

    while (!isUnique) {
      const randomNum = Math.floor(100000 + Math.random() * 900000);
      fleetId = `${prefix}${randomNum}`;

      const existing = await this.vehicleRepository.findOne({ fleetId });

      if (!existing) {
        isUnique = true;
      }
    }

    return fleetId;
  }

  async updateVehicleDetails(vehicleDomain: VehicleDomain): Promise<void> {
    const vehicle = await this.vehicleRepository.findOne({
      id: vehicleDomain.id,
    });
    if (!vehicle) {
      throw new VehicleNotFoundException(vehicleDomain.id);
    }

    const vehicleType = await this.vehicleTypeRepository.findOne({
      id: vehicleDomain.vehicleTypeId,
    });
    if (!vehicleType) {
      throw new VehicleTypeNotFoundException(vehicleDomain.vehicleTypeId);
    }

    await this.vehicleRepository.update(vehicleDomain);
    return;
  }

  async deleteVehicle(vehicleId: VehicleDomain['id']): Promise<void> {
    const vehicle = await this.vehicleRepository.findOne({ id: vehicleId });
    if (!vehicle) {
      throw new VehicleNotFoundException(vehicleId);
    }

    // Additional checks could be added here
    // For example, check if vehicle is currently in use in a time clock session

    vehicle.isDeleted = true;
    vehicle.deletedAt = new Date();
    await this.vehicleRepository.update(vehicle);
    return;
  }
}
