import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsUUID,
  Min,
  Max,
  IsInt,
  IsPositive,
} from 'class-validator';
import { trimTransformer } from '@utils/transformers/lower-case.transformer';

export class CreateVehicleDto {
  @AutoMap()
  @ApiProperty({ example: '66fedab8-7820-4cca-b8ca-cf59ade1aada' })
  @IsUUID(4)
  @IsNotEmpty()
  vehicleTypeId: string;

  @AutoMap()
  @ApiProperty({ example: 'CAR001' })
  @IsString()
  @IsNotEmpty()
  @Transform(trimTransformer)
  fleetId: string;

  @AutoMap()
  @ApiProperty({ example: 'Dodge' })
  @IsString()
  @IsNotEmpty()
  @Transform(trimTransformer)
  make: string;

  @AutoMap()
  @ApiProperty({ example: 'Caravan' })
  @IsString()
  @IsNotEmpty()
  @Transform(trimTransformer)
  model: string;

  @AutoMap()
  @ApiProperty({ example: 2000 })
  @IsInt()
  @Min(1990)
  @Max(new Date().getFullYear())
  @IsNotEmpty()
  @Transform(({ value }) => (value && value !== '' ? Number(value) : undefined))
  year: number;

  @AutoMap()
  @ApiProperty({ example: 'H4G 1NX' })
  @IsString()
  @IsNotEmpty()
  @Transform(trimTransformer)
  licensePlate: string;

  @AutoMap()
  @ApiProperty({ example: 'WAUFFAFL8DN005985' })
  @IsString()
  @IsOptional()
  @Transform(trimTransformer)
  vin: string;

  @AutoMap()
  @ApiProperty({ example: ['Boxes', 'Skid'] })
  @IsNotEmpty()
  @Transform(trimTransformer)
  packageType: Array<string>;

  @AutoMap()
  @ApiProperty({ example: 3000 })
  @IsInt()
  @IsPositive()
  @IsNotEmpty()
  @Transform(({ value }) => (value && value !== '' ? Number(value) : undefined))
  maxWeight: number;

  @AutoMap()
  @ApiProperty({ example: 'Montreal Branch' })
  @IsString()
  @IsNotEmpty()
  @Transform(trimTransformer)
  branch: string;

  @AutoMap()
  @ApiProperty({ example: 15000 })
  @IsInt()
  @Min(0)
  @IsNotEmpty()
  @Transform(({ value }) => (value && value !== '' ? Number(value) : undefined))
  currentOdometer: number;

  @AutoMap()
  @ApiProperty({ example: 'User1' })
  @IsString()
  @IsOptional()
  @Transform(trimTransformer)
  ownedBy: string;

  @AutoMap()
  @ApiProperty({ default: 'Nice Vehicle' })
  @IsString()
  @IsOptional()
  @Transform(trimTransformer)
  notes: string;
}
