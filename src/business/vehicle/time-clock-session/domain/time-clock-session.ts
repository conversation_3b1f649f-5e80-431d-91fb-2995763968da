import { AutoMap } from '@automapper/classes';
import { UserEntity } from '../../../user/users/infrastructure/entities/user.entity';

export class TimeClockSessionDomain {
  @AutoMap()
  id: string;

  @AutoMap()
  tenantId: string;

  @AutoMap()
  vehicleId: string;

  @AutoMap()
  driver: UserEntity;

  @AutoMap()
  driverId: string;

  @AutoMap()
  date: Date;

  @AutoMap()
  startTime: Date;

  @AutoMap()
  endTime: Date;

  @AutoMap()
  distanceTraveled: number;

  @AutoMap()
  source: string;

  @AutoMap()
  addedBy: string;

  @AutoMap()
  startOdometer: number;

  @AutoMap()
  endOdometer: number;

  @AutoMap()
  notes: string;

  @AutoMap()
  metaData: Record<string, any>[];

  @AutoMap()
  isDeleted: boolean;

  @AutoMap()
  deletedAt: Date;

  @AutoMap()
  createdAt: Date;

  @AutoMap()
  updatedAt: Date;

  @AutoMap()
  createdBy: string;

  @AutoMap()
  updatedBy: string;
}
