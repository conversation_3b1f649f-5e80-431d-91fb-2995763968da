import { Modu<PERSON> } from '@nestjs/common';
import { MasterModule } from './master/master.module';
import { TenantManagementModule } from './tenant-management/tenant-management.module';
import { SystemSettingsModule } from './system-settings/system-settings.module';
import { Ontime360MigrationModule } from './ontime360-migration/ontime360-migration.module';

@Module({
  imports: [
    MasterModule,
    TenantManagementModule,
    SystemSettingsModule,
    Ontime360MigrationModule,
  ],
})
export class AdminModule {}
